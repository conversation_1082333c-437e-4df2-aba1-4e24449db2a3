<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Data.SqlClient" Version="6.1.0" />
      <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
      <PackageReference Include="MySql.Data" Version="9.4.0" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
      <PackageReference Include="Spectre.Console" Version="0.50.1-preview.0.22" />
      <PackageReference Include="YamlDotNet" Version="16.3.0" />
    </ItemGroup>
	
	<ItemGroup>
	  <None Include="config.yaml">
		<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
