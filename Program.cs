using System;
using HeliosETL.Services;
using HeliosETL.Services.Extractors;
using Spectre.Console;
using Serilog;

namespace HeliosETL
{
    public class Program
    {
        static ILogger? logger = null;

        static void Main(string[] args)
        {
            try
            {
                // Initialize logging first with defaults to avoid circular dependency
                LoggingService.Instance.InitializeWithDefaults();
                logger = LoggingService.Instance.GetLogger<Program>();

                logger.Information("HeliosETL application starting");

                Database db = new Database();

                AnsiConsole.Status()
                    .Start("Initializing...", ctx =>
                    {
                        AnsiConsole.MarkupLine("[yellow]Loading configuration...[/]");
                        try
                        {
                            ConfigurationService.Instance.GetConfiguration();
                            logger.Information("Configuration loaded successfully");
                        }
                        catch (Exception ex)
                        {
                            AnsiConsole.MarkupLine($"[red]Error loading configuration.[/]");
                            logger.Error(ex, "Failed to load configuration");
                            return;
                        }
                        ctx.Status("Configuration loaded.");

                        AnsiConsole.MarkupLine("[yellow]Setting up database...[/]");
                        if (!db.SetupDatabase())
                        {
                            AnsiConsole.MarkupLine("[red]Database setup failed![/]");
                            return;
                        }

                        AnsiConsole.MarkupLine("[yellow]Testing connection...[/]");

                        if (!db.testConnexions())
                        {
                            AnsiConsole.MarkupLine("[red]Connection failed![/]");
                            return;
                        }

                        AnsiConsole.MarkupLine("[green]Done![/]");
                        ctx.Status("Initialization complete. Now starting process.");
                        bool res = Run(ctx);
                        if (!res)
                        {
                            AnsiConsole.MarkupLine("[red]Process failed![/]");
                            logger.Error("Process execution failed");
                        }
                        else
                        {
                            logger.Information("Process completed successfully");
                        }
                    });

                AnsiConsole.MarkupLine("[green]Program terminated.[/]");
                logger.Information("HeliosETL application terminated successfully");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]Fatal error: {ex.Message}[/]");
                logger?.Fatal(ex, "Fatal error occurred in main application");
                Environment.Exit(1);
            }
            finally
            {
                LoggingService.Instance.Dispose();
            }
        }

        static bool Run(StatusContext ctx)
        {
            CachedMemory cache = CachedMemory.Get();
            if (cache == null)
                return false;
            CachedMemory.ctx = ctx;
            cache.LoadCache();
            
            ctx.Status("Starting ETL processing.");
            return false;
        }
    }
}