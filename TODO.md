# TODO Plan for V2 Repositories - Joined Entities Implementation

## 📋 Current Status Analysis

### ✅ Repositories with COMPLETE CRUD Implementation
All V2 repositories have been analyzed and **ALL** have complete CRUD implementations:
- ✅ GetAll()
- ✅ GetById(int id)
- ✅ Add(TEntity entity)
- ✅ Update(TEntity entity)
- ✅ Delete(TEntity entity)

### 🔗 Repositories with Joined Entities Methods (Already Implemented)
The following repositories already have methods to retrieve joined/related entities:

1. **TypeProjetRepository** ✅
   - Method: `GetDomainesMetierByTypeProjet(long oidTypeProjet)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_projet_type_has_domaine_metier`

2. **TypeMissionRepository** ✅
   - Method: `GetDomainesMetierByTypeMission(long oidTypeMission)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_mission_type_has_domaine_metier`

3. **CommanditaireRepository** ✅
   - Method: `GetPersonnesByCommanditaire(long oidCommanditaire)`
   - Returns: `HashSet<Personne>`
   - Join Table: `hls_commanditaire_has_personne`

4. **TypeDocumentRepository** ✅
   - Method: `GetDomainesMetierByTypeDocument(long oidTypeDocument)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_document_type_has_domaine_metier`

## 🎯 TODO Tasks for Missing Joined Entities Methods

### Priority 1: Core Entity Relationships

#### 1. **IssueRepository**
**Status**: ⚠️ Missing joined entities methods AND relationship management
**TODO Items**:

**Retrieval Methods:**
- [ ] Add `GetPersonnesByIssue(long oidIssue)` method
  - Join Table: `hls_issue_has_personne`
  - Returns: `HashSet<Personne>`
- [ ] Add `GetPieceJointesByIssue(long oidIssue)` method
  - Direct relationship via `issue_oid` foreign key
  - Returns: `HashSet<IssuePieceJointe>`
- [ ] Add `GetJournalDetailsByIssue(long oidIssue)` method
  - Direct relationship via `issue_oid` foreign key
  - Returns: `HashSet<JournalDetails>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

**⚠️ CRITICAL: Relationship Management in Add/Update Methods:**
- [ ] **Update `Add(AbstractIssue entity)` method** to handle relationships:
  - Add related persons to `hls_issue_has_personne` table
  - Add related attachments with proper `issue_oid` foreign key
  - Add initial journal entry with proper `issue_oid` foreign key
- [ ] **Update `Update(AbstractIssue entity)` method** to handle relationships:
  - Update/sync persons in `hls_issue_has_personne` table
  - Handle attachment updates (add new, remove deleted)
  - Add journal entries for changes with proper `issue_oid` foreign key
- [ ] **Add relationship management methods:**
  - `AddPersonneToIssue(long oidIssue, long oidPersonne)`
  - `RemovePersonneFromIssue(long oidIssue, long oidPersonne)`
  - `SyncPersonnesForIssue(long oidIssue, HashSet<long> personneOids)`

#### 2. **MissionRepository**
**Status**: ⚠️ Missing joined entities methods AND relationship management
**TODO Items**:

**Retrieval Methods:**
- [ ] Add `GetPersonnesByMission(long oidMission)` method
  - Join Table: `hls_mission_has_personne`
  - Returns: `HashSet<Personne>`
- [ ] Add `GetIssuesByMission(long oidMission)` method
  - Direct relationship via `mission_oid` foreign key
  - Returns: `HashSet<AbstractIssue>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

**⚠️ CRITICAL: Relationship Management in Add/Update Methods:**
- [ ] **Update `Add(Mission entity)` method** to handle relationships:
  - Add related persons to `hls_mission_has_personne` table
  - Handle any initial issues with proper `mission_oid` foreign key
- [ ] **Update `Update(Mission entity)` method** to handle relationships:
  - Update/sync persons in `hls_mission_has_personne` table
  - Update related issues if mission details change
- [ ] **Add relationship management methods:**
  - `AddPersonneToMission(long oidMission, long oidPersonne)`
  - `RemovePersonneFromMission(long oidMission, long oidPersonne)`
  - `SyncPersonnesForMission(long oidMission, HashSet<long> personneOids)`

#### 3. **PersonneRepository**
**Status**: ⚠️ Missing joined entities methods AND relationship management
**TODO Items**:

**Retrieval Methods:**
- [ ] Add `GetCommanditairesByPersonne(long oidPersonne)` method
  - Join Table: `hls_commanditaire_has_personne`
  - Returns: `HashSet<Commanditaire>`
- [ ] Add `GetMissionsByPersonne(long oidPersonne)` method
  - Join Table: `hls_mission_has_personne`
  - Returns: `HashSet<Mission>`
- [ ] Add `GetIssuesByPersonne(long oidPersonne)` method
  - Join Table: `hls_issue_has_personne`
  - Returns: `HashSet<AbstractIssue>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

**⚠️ CRITICAL: Relationship Management in Add/Update Methods:**
- [ ] **Update `Add(Personne entity)` method** to handle relationships:
  - Add to commanditaire relationships if specified
  - Add to mission relationships if specified
  - Add to issue relationships if specified
- [ ] **Update `Update(Personne entity)` method** to handle relationships:
  - Sync commanditaire relationships in `hls_commanditaire_has_personne`
  - Sync mission relationships in `hls_mission_has_personne`
  - Sync issue relationships in `hls_issue_has_personne`
- [ ] **Add relationship management methods:**
  - `AddPersonneToCommanditaire(long oidPersonne, long oidCommanditaire)`
  - `RemovePersonneFromCommanditaire(long oidPersonne, long oidCommanditaire)`
  - `AddPersonneToMission(long oidPersonne, long oidMission)`
  - `RemovePersonneFromMission(long oidPersonne, long oidMission)`
  - `AddPersonneToIssue(long oidPersonne, long oidIssue)`
  - `RemovePersonneFromIssue(long oidPersonne, long oidIssue)`

### Priority 2: Lookup Table Relationships

#### 4. **DomaineMetierRepository**
**Status**: ⚠️ Missing reverse relationship methods AND relationship management
**TODO Items**:

**Retrieval Methods:**
- [ ] Add `GetTypeProjetsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_projet_type_has_domaine_metier`
  - Returns: `HashSet<TypeProjet>`
- [ ] Add `GetTypeMissionsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_mission_type_has_domaine_metier`
  - Returns: `HashSet<TypeMission>`
- [ ] Add `GetTypeDocumentsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_document_type_has_domaine_metier`
  - Returns: `HashSet<TypeDocument>`

**⚠️ CRITICAL: Relationship Management in Add/Update Methods:**
- [ ] **Update `Add(DomaineMetier entity)` method** to handle relationships:
  - Add to type projet relationships if specified
  - Add to type mission relationships if specified
  - Add to type document relationships if specified
- [ ] **Update `Update(DomaineMetier entity)` method** to handle relationships:
  - Sync relationships in all related junction tables
- [ ] **Add relationship management methods:**
  - `AddDomaineMetierToTypeProjet(long oidDomaineMetier, long oidTypeProjet)`
  - `RemoveDomaineMetierFromTypeProjet(long oidDomaineMetier, long oidTypeProjet)`
  - `AddDomaineMetierToTypeMission(long oidDomaineMetier, long oidTypeMission)`
  - `RemoveDomaineMetierFromTypeMission(long oidDomaineMetier, long oidTypeMission)`
  - `AddDomaineMetierToTypeDocument(long oidDomaineMetier, long oidTypeDocument)`
  - `RemoveDomaineMetierFromTypeDocument(long oidDomaineMetier, long oidTypeDocument)`

#### 5. **JournalRepository**
**Status**: ⚠️ Missing joined entities methods AND relationship management
**TODO Items**:

**Retrieval Methods:**
- [ ] Add `GetJournalDetailsByJournal(long oidJournal)` method
  - Direct relationship via `journal_oid` foreign key
  - Returns: `HashSet<JournalDetails>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

**⚠️ CRITICAL: Relationship Management in Add/Update Methods:**
- [ ] **Update `Add(Journal entity)` method** to handle relationships:
  - Add initial journal details if specified
- [ ] **Update `Update(Journal entity)` method** to handle relationships:
  - Add new journal details for changes made
- [ ] **Add relationship management methods:**
  - `AddJournalDetailToJournal(long oidJournal, JournalDetails detail)`

### Priority 2.5: CRITICAL - Fix Existing Repositories with Joined Entities

#### **TypeProjetRepository** ⚠️
**Status**: Has joined entities method BUT missing relationship management in Add/Update
**CRITICAL TODO Items**:
- [ ] **Update `Add(TypeProjet entity)` method** to handle DomaineMetier relationships:
  - Add related domaines metier to `hls_projet_type_has_domaine_metier` table
  - Handle `entity.DomainesMetier` collection if populated
- [ ] **Update `Update(TypeProjet entity)` method** to handle DomaineMetier relationships:
  - Sync domaines metier in `hls_projet_type_has_domaine_metier` table
  - Remove old relationships, add new ones
- [ ] **Add relationship management methods:**
  - `AddDomaineMetierToTypeProjet(long oidTypeProjet, long oidDomaineMetier)`
  - `RemoveDomaineMetierFromTypeProjet(long oidTypeProjet, long oidDomaineMetier)`
  - `SyncDomainesMetierForTypeProjet(long oidTypeProjet, HashSet<long> domaineMetierOids)`

#### **TypeMissionRepository** ⚠️
**Status**: Has joined entities method BUT missing relationship management in Add/Update
**CRITICAL TODO Items**:
- [ ] **Update `Add(TypeMission entity)` method** to handle DomaineMetier relationships:
  - Add related domaines metier to `hls_mission_type_has_domaine_metier` table
  - Handle `entity.DomainesMetier` collection if populated
- [ ] **Update `Update(TypeMission entity)` method** to handle DomaineMetier relationships:
  - Sync domaines metier in `hls_mission_type_has_domaine_metier` table
- [ ] **Add relationship management methods:**
  - `AddDomaineMetierToTypeMission(long oidTypeMission, long oidDomaineMetier)`
  - `RemoveDomaineMetierFromTypeMission(long oidTypeMission, long oidDomaineMetier)`
  - `SyncDomainesMetierForTypeMission(long oidTypeMission, HashSet<long> domaineMetierOids)`

#### **CommanditaireRepository** ⚠️
**Status**: Has joined entities method BUT missing relationship management in Add/Update
**CRITICAL TODO Items**:
- [ ] **Update `Add(Commanditaire entity)` method** to handle Personne relationships:
  - Add related personnes to `hls_commanditaire_has_personne` table
  - Handle related persons if specified
- [ ] **Update `Update(Commanditaire entity)` method** to handle Personne relationships:
  - Sync personnes in `hls_commanditaire_has_personne` table
- [ ] **Add relationship management methods:**
  - `AddPersonneToCommanditaire(long oidCommanditaire, long oidPersonne)`
  - `RemovePersonneFromCommanditaire(long oidCommanditaire, long oidPersonne)`
  - `SyncPersonnesForCommanditaire(long oidCommanditaire, HashSet<long> personneOids)`

#### **TypeDocumentRepository** ⚠️
**Status**: Has joined entities method BUT missing relationship management in Add/Update
**CRITICAL TODO Items**:
- [ ] **Update `Add(TypeDocument entity)` method** to handle DomaineMetier relationships:
  - Add related domaines metier to `hls_document_type_has_domaine_metier` table
- [ ] **Update `Update(TypeDocument entity)` method** to handle DomaineMetier relationships:
  - Sync domaines metier in `hls_document_type_has_domaine_metier` table
- [ ] **Add relationship management methods:**
  - `AddDomaineMetierToTypeDocument(long oidTypeDocument, long oidDomaineMetier)`
  - `RemoveDomaineMetierFromTypeDocument(long oidTypeDocument, long oidDomaineMetier)`
  - `SyncDomainesMetierForTypeDocument(long oidTypeDocument, HashSet<long> domaineMetierOids)`

### Priority 3: Enhancement Tasks

#### 6. **General Repository Enhancements**
**TODO Items**:
- [ ] Add `//TODO` comments for missing relationship management methods
- [ ] Implement relationship management methods (Add/Remove related entities)
- [ ] Add validation for foreign key constraints
- [ ] Add transaction support for complex operations
- [ ] Add bulk operations for better performance
- [ ] Add caching mechanisms for frequently accessed data

#### 7. **Code Quality Improvements**
**TODO Items**:
- [ ] Add comprehensive error handling for JOIN operations
- [ ] Add logging for relationship operations
- [ ] Add parameter validation for all new methods
- [ ] Add XML documentation for all new methods
- [ ] Add unit tests for all joined entities methods

## 🔧 Implementation Guidelines

### Method Naming Convention
- **Retrieval**: `Get{RelatedEntity}By{CurrentEntity}(long oid{CurrentEntity})`
  - Example: `GetPersonnesByIssue(long oidIssue)`
- **Relationship Management**: `Add{RelatedEntity}To{CurrentEntity}(long oid{CurrentEntity}, long oid{RelatedEntity})`
  - Example: `AddPersonneToIssue(long oidIssue, long oidPersonne)`
- **Sync Methods**: `Sync{RelatedEntity}For{CurrentEntity}(long oid{CurrentEntity}, HashSet<long> relatedOids)`
  - Example: `SyncPersonnesForIssue(long oidIssue, HashSet<long> personneOids)`

### Return Types
- Use `HashSet<T>` for collections to avoid duplicates
- Use `IEnumerable<T>` for read-only collections when appropriate

### ⚠️ CRITICAL: Add/Update Method Pattern
When updating Add/Update methods, follow this pattern:

```csharp
public override void Add(EntityType entity)
{
    // 1. First, add the main entity
    OpenConnection();
    using (var transaction = _connection.BeginTransaction())
    {
        try
        {
            // Insert main entity
            using (var command = new MySqlCommand("INSERT INTO main_table (...) VALUES (...)", (MySqlConnection)_connection))
            {
                // ... add parameters and execute
            }

            // 2. Then handle relationships
            if (entity.RelatedEntities != null && entity.RelatedEntities.Any())
            {
                foreach (var relatedEntity in entity.RelatedEntities)
                {
                    using (var relationCommand = new MySqlCommand(
                        "INSERT INTO junction_table (main_oid, related_oid) VALUES (@mainOid, @relatedOid)",
                        (MySqlConnection)_connection))
                    {
                        relationCommand.Parameters.AddWithValue("@mainOid", entity.Oid);
                        relationCommand.Parameters.AddWithValue("@relatedOid", relatedEntity.Oid);
                        relationCommand.ExecuteNonQuery();
                    }
                }
            }

            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }
    CloseConnection();
}

public override void Update(EntityType entity)
{
    OpenConnection();
    using (var transaction = _connection.BeginTransaction())
    {
        try
        {
            // 1. Update main entity
            using (var command = new MySqlCommand("UPDATE main_table SET ... WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                // ... update main entity
            }

            // 2. Sync relationships (remove old, add new)
            // First, remove existing relationships
            using (var deleteCommand = new MySqlCommand(
                "DELETE FROM junction_table WHERE main_oid = @mainOid",
                (MySqlConnection)_connection))
            {
                deleteCommand.Parameters.AddWithValue("@mainOid", entity.Oid);
                deleteCommand.ExecuteNonQuery();
            }

            // Then, add current relationships
            if (entity.RelatedEntities != null && entity.RelatedEntities.Any())
            {
                foreach (var relatedEntity in entity.RelatedEntities)
                {
                    using (var insertCommand = new MySqlCommand(
                        "INSERT INTO junction_table (main_oid, related_oid) VALUES (@mainOid, @relatedOid)",
                        (MySqlConnection)_connection))
                    {
                        insertCommand.Parameters.AddWithValue("@mainOid", entity.Oid);
                        insertCommand.Parameters.AddWithValue("@relatedOid", relatedEntity.Oid);
                        insertCommand.ExecuteNonQuery();
                    }
                }
            }

            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }
    CloseConnection();
}
```

### Error Handling
- **ALWAYS use transactions** for operations involving multiple tables
- Add proper exception handling for database operations
- Log errors appropriately using the existing logging infrastructure
- Return empty collections instead of null when no results found
- Rollback transactions on any error

### Performance Considerations
- Use parameterized queries to prevent SQL injection
- Consider adding indexes on foreign key columns
- Implement lazy loading where appropriate
- Add caching for frequently accessed lookup data
- Use batch operations for multiple relationship inserts

## 📝 Critical Implementation Notes

### ⚠️ **MOST IMPORTANT**: Transaction Management
- **ALL relationship operations MUST use database transactions**
- If any part of the relationship management fails, the entire operation should rollback
- This prevents orphaned records and maintains data integrity

### 🔍 **Current State Analysis**
- ✅ All repositories have complete CRUD operations implemented
- ⚠️ **CRITICAL ISSUE**: Existing repositories with joined entities methods are missing relationship management in Add/Update
- 🎯 **PRIORITY**: Fix existing repositories first, then add missing joined entities methods

### 🏗️ **Implementation Order**
1. **FIRST**: Fix existing repositories (TypeProjetRepository, TypeMissionRepository, CommanditaireRepository, TypeDocumentRepository)
2. **SECOND**: Add missing joined entities methods to other repositories
3. **THIRD**: Add relationship management to newly created methods

### 📋 **Junction Tables Identified**
- `hls_activite_has_domaine` (AbstractActivite ↔ DomaineMetier) Fields: activite_oid, domaine_oid
- `hls_commanditaire_has_personne` (Commanditaire ↔ Personne) Fields: commanditaire_oid, personne_oid
- `hls_document_type_has_domaine_metier` (TypeDocument ↔ DomaineMetier) Fields: document_type_oid, domaine_metier_oid
- `hls_issue_has_intervenant` (Issue ↔ Personne) Fields: issue_oid, personne_oid
- `hls_issue_origine_type_has_domaine_metier` (IssueOrigine ↔ DomaineMetier) Fields: issue_origine_oid, domaine_metier_oid
- `hls_issue_priorite_has_domaine_metier` (IssuePriorite ↔ DomaineMetier) Fields: issue_priorite_oid, domaine_metier_oid
- `hls_issue_statut_has_domaine_metier` (IssueStatut ↔ DomaineMetier) Fields: issue_statut_oid, domaine_metier_oid
- `hls_mission_type_has_domaine_metier` (TypeMission ↔ DomaineMetier) Fields: mission_type_oid, domaine_metier_oid
- `hls_personne_type_has_domaine_metier` (PersonneType ↔ DomaineMetier) Fields: personne_type_oid, domaine_metier_oid
- `hls_projet_type_has_domaine_metier` (TypeProjet ↔ DomaineMetier) Fields: projet_type_oid, domaine_metier_oid

### 🔗 **Foreign Key Relationships Identified**
- `hls_issue_piece_jointe.issue_oid` → `hls_issue.Oid`
- `hls_journal_details.journal_oid` → `hls_journal.Oid`
- `hls_issue.mission_oid` → `hls_mission.Oid`

### 🚨 **Common Pitfalls to Avoid**
- ❌ Don't forget to handle NULL collections in entities
- ❌ Don't add relationships without checking if they already exist
- ❌ Don't forget to remove old relationships before adding new ones in Update methods
- ❌ Don't forget to use transactions for multi-table operations
- ❌ Don't hardcode relationship table names - consider using constants

### 💡 **Best Practices**
- ✅ Always validate foreign key existence before creating relationships
- ✅ Use HashSet<long> for OID collections to avoid duplicates
- ✅ Log relationship operations for debugging
- ✅ Consider adding cascade delete handling
- ✅ Add validation to prevent circular relationships where applicable

### 🔧 **Testing Strategy**
- Test Add operations with and without relationships
- Test Update operations that add, remove, and modify relationships
- Test Delete operations and verify relationship cleanup
- Test transaction rollback scenarios
- Test with NULL and empty relationship collections
