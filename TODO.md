# TODO Plan for V2 Repositories - Joined Entities Implementation

## 📋 Current Status Analysis

### ✅ Repositories with COMPLETE CRUD Implementation
All V2 repositories have been analyzed and **ALL** have complete CRUD implementations:
- ✅ GetAll()
- ✅ GetById(int id)
- ✅ Add(TEntity entity)
- ✅ Update(TEntity entity)
- ✅ Delete(TEntity entity)

### 🔗 Repositories with Joined Entities Methods (Already Implemented)
The following repositories already have methods to retrieve joined/related entities:

1. **TypeProjetRepository** ✅
   - Method: `GetDomainesMetierByTypeProjet(long oidTypeProjet)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_projet_type_has_domaine_metier`

2. **TypeMissionRepository** ✅
   - Method: `GetDomainesMetierByTypeMission(long oidTypeMission)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_mission_type_has_domaine_metier`

3. **CommanditaireRepository** ✅
   - Method: `GetPersonnesByCommanditaire(long oidCommanditaire)`
   - Returns: `HashSet<Personne>`
   - Join Table: `hls_commanditaire_has_personne`

4. **TypeDocumentRepository** ✅
   - Method: `GetDomainesMetierByTypeDocument(long oidTypeDocument)`
   - Returns: `HashSet<DomaineMetier>`
   - Join Table: `hls_document_type_has_domaine_metier`

## 🎯 TODO Tasks for Missing Joined Entities Methods

### Priority 1: Core Entity Relationships

#### 1. **IssueRepository** 
**Status**: ⚠️ Missing joined entities methods
**TODO Items**:
- [ ] Add `GetPersonnesByIssue(long oidIssue)` method
  - Join Table: `hls_issue_has_personne`
  - Returns: `HashSet<Personne>`
- [ ] Add `GetPieceJointesByIssue(long oidIssue)` method
  - Direct relationship via `issue_oid` foreign key
  - Returns: `HashSet<IssuePieceJointe>`
- [ ] Add `GetJournalDetailsByIssue(long oidIssue)` method
  - Direct relationship via `issue_oid` foreign key
  - Returns: `HashSet<JournalDetails>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities
- [ ] Add methods to manage many-to-many relationships (Add/Remove persons)

#### 2. **MissionRepository**
**Status**: ⚠️ Missing joined entities methods
**TODO Items**:
- [ ] Add `GetPersonnesByMission(long oidMission)` method
  - Join Table: `hls_mission_has_personne`
  - Returns: `HashSet<Personne>`
- [ ] Add `GetIssuesByMission(long oidMission)` method
  - Direct relationship via `mission_oid` foreign key
  - Returns: `HashSet<AbstractIssue>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities
- [ ] Add methods to manage many-to-many relationships (Add/Remove persons)

#### 3. **PersonneRepository**
**Status**: ⚠️ Missing joined entities methods
**TODO Items**:
- [ ] Add `GetCommanditairesByPersonne(long oidPersonne)` method
  - Join Table: `hls_commanditaire_has_personne`
  - Returns: `HashSet<Commanditaire>`
- [ ] Add `GetMissionsByPersonne(long oidPersonne)` method
  - Join Table: `hls_mission_has_personne`
  - Returns: `HashSet<Mission>`
- [ ] Add `GetIssuesByPersonne(long oidPersonne)` method
  - Join Table: `hls_issue_has_personne`
  - Returns: `HashSet<AbstractIssue>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

### Priority 2: Lookup Table Relationships

#### 4. **DomaineMetierRepository**
**Status**: ⚠️ Missing reverse relationship methods
**TODO Items**:
- [ ] Add `GetTypeProjetsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_projet_type_has_domaine_metier`
  - Returns: `HashSet<TypeProjet>`
- [ ] Add `GetTypeMissionsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_mission_type_has_domaine_metier`
  - Returns: `HashSet<TypeMission>`
- [ ] Add `GetTypeDocumentsByDomaineMetier(long oidDomaineMetier)` method
  - Join Table: `hls_document_type_has_domaine_metier`
  - Returns: `HashSet<TypeDocument>`

#### 5. **JournalRepository**
**Status**: ⚠️ Missing joined entities methods
**TODO Items**:
- [ ] Add `GetJournalDetailsByJournal(long oidJournal)` method
  - Direct relationship via `journal_oid` foreign key
  - Returns: `HashSet<JournalDetails>`
- [ ] Update `GetAll()` and `GetById()` to optionally load related entities

### Priority 3: Enhancement Tasks

#### 6. **General Repository Enhancements**
**TODO Items**:
- [ ] Add `//TODO` comments for missing relationship management methods
- [ ] Implement relationship management methods (Add/Remove related entities)
- [ ] Add validation for foreign key constraints
- [ ] Add transaction support for complex operations
- [ ] Add bulk operations for better performance
- [ ] Add caching mechanisms for frequently accessed data

#### 7. **Code Quality Improvements**
**TODO Items**:
- [ ] Add comprehensive error handling for JOIN operations
- [ ] Add logging for relationship operations
- [ ] Add parameter validation for all new methods
- [ ] Add XML documentation for all new methods
- [ ] Add unit tests for all joined entities methods

## 🔧 Implementation Guidelines

### Method Naming Convention
- Use pattern: `Get{RelatedEntity}By{CurrentEntity}(long oid{CurrentEntity})`
- Example: `GetPersonnesByIssue(long oidIssue)`

### Return Types
- Use `HashSet<T>` for collections to avoid duplicates
- Use `IEnumerable<T>` for read-only collections when appropriate

### Error Handling
- Add proper exception handling for database operations
- Log errors appropriately using the existing logging infrastructure
- Return empty collections instead of null when no results found

### Performance Considerations
- Use parameterized queries to prevent SQL injection
- Consider adding indexes on foreign key columns
- Implement lazy loading where appropriate
- Add caching for frequently accessed lookup data

## 📝 Notes
- All repositories already have complete CRUD operations implemented
- Focus is now on adding joined entities retrieval methods
- Existing joined entities methods in TypeProjetRepository, TypeMissionRepository, CommanditaireRepository, and TypeDocumentRepository can serve as templates
- Consider adding optional parameters to existing GetAll() and GetById() methods to include related entities
