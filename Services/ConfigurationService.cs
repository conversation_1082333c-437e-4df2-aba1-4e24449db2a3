using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using HeliosETL.Models.Configuration;

namespace HeliosETL.Services;

public class ConfigurationService
{
    private static ConfigurationService? _instance;
    private static readonly object _lock = new object();
    private AppConfig? _config;
    private readonly string _configFilePath;

    private ConfigurationService()
    {
        _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.yaml");
    }

    public static ConfigurationService Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new ConfigurationService();
                    }
                }
            }
            return _instance;
        }
    }

    public AppConfig GetConfiguration()
    {
        if (_config == null)
        {
            LoadConfiguration();
        }
        return _config ?? throw new InvalidOperationException("Configuration could not be loaded.");
    }

    private void LoadConfiguration()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                throw new FileNotFoundException($"Configuration file not found: {_configFilePath}");
            }

            var yaml = File.ReadAllText(_configFilePath);
            var deserializer = new DeserializerBuilder()
                .WithNamingConvention(CamelCaseNamingConvention.Instance)
                .Build();

            _config = deserializer.Deserialize<AppConfig>(yaml);

            if (_config == null)
            {
                throw new InvalidOperationException("Failed to deserialize configuration file.");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error loading configuration: {ex.Message}", ex);
        }
    }

    public void ReloadConfiguration()
    {
        _config = null;
        LoadConfiguration();
    }

    public DatabaseConfig GetDatabaseConfig()
    {
        return GetConfiguration().Database;
    }
}
