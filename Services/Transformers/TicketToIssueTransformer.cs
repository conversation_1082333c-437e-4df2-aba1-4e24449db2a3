namespace HeliosETL.Services.Transformers;

/// <summary>
/// Represents a transformer for converting ticket data from Helios v1 to a format compatible with Helios v2.
/// </summary>
/// <remarks>
/// <para>Le transformer ticket to Issue d'helios v1 à v2 va nécessiter:</para>
/// <para>1. Récupérer l'ensemble des tickets</para>
/// <para>2. Récupérer l'ensemble des ticketsHistorique</para>
/// <para>3. Valider les données</para>
/// <para>4. Transformer les données vers</para>
/// <para>  - Issue</para>
/// <para>  - Journal</para>
/// <para>  - DomaineMetier</para>
/// <para>  - Activite</para>
/// <para>  - Mission</para>
/// <para>5. Charger les données dans la nouvelle base MySQL</para>
/// </remarks>
public class TicketToIssueTransformer : ITransformer
{
    public T Prepare<T>()
    {
        throw new NotImplementedException();
    }

    public T Transform<T>()
    {
        throw new NotImplementedException();
    }

    public bool Validate()
    {
        throw new NotImplementedException();
    }
}