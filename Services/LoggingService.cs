using Serilog;
using Serilog.Events;
using HeliosETL.Models.Configuration;

namespace HeliosETL.Services;

public class LoggingService
{
    private static LoggingService? _instance;
    private static readonly object _lock = new object();
    private ILogger? _logger;
    private bool _isInitialized = false;

    private LoggingService()
    {
    }

    public static LoggingService Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new LoggingService();
                    }
                }
            }
            return _instance;
        }
    }

    public void Initialize(LoggingConfig? config = null)
    {
        if (_isInitialized)
            return;

        try
        {
            // Use provided config or try to get from ConfigurationService
            config ??= ConfigurationService.Instance.GetConfiguration().Logging;
            
            var loggerConfig = new LoggerConfiguration();

            // Configure console logging
            if (config.Console.Enabled)
            {
                var consoleLevel = ParseLogLevel(config.Console.MinimumLevel);
                loggerConfig.WriteTo.Console(
                    restrictedToMinimumLevel: consoleLevel,
                    outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
                );
            }

            // Configure file logging
            if (config.File.Enabled)
            {
                var fileLevel = ParseLogLevel(config.File.MinimumLevel);
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, config.File.LogDirectory);
                
                // Ensure log directory exists
                Directory.CreateDirectory(logDirectory);
                
                var logFilePath = Path.Combine(logDirectory, config.File.FileNameTemplate);
                
                loggerConfig.WriteTo.File(
                    path: logFilePath,
                    restrictedToMinimumLevel: fileLevel,
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: config.File.RetainedFileCountLimit,
                    fileSizeLimitBytes: config.File.FileSizeLimitBytes,
                    rollOnFileSizeLimit: true,
                    outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"
                );
            }

            // Set minimum level to the most verbose of enabled sinks
            var minLevel = LogEventLevel.Information;
            if (config.Console.Enabled)
            {
                var consoleLevel = ParseLogLevel(config.Console.MinimumLevel);
                minLevel = consoleLevel;
            }
            if (config.File.Enabled)
            {
                var fileLevel = ParseLogLevel(config.File.MinimumLevel);
                if (!config.Console.Enabled || fileLevel < minLevel)
                    minLevel = fileLevel;
            }

            loggerConfig.MinimumLevel.Is(minLevel);

            _logger = loggerConfig.CreateLogger();
            Log.Logger = _logger;
            
            _isInitialized = true;
            
            _logger.Information("Logging service initialized successfully");
        }
        catch (Exception ex)
        {
            // Fallback to file-only logging if configuration fails
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Directory.CreateDirectory(logDirectory);
            var logFilePath = Path.Combine(logDirectory, "helios-etl-fallback-.log");

            _logger = new LoggerConfiguration()
                .WriteTo.File(logFilePath, rollingInterval: RollingInterval.Day)
                .CreateLogger();

            _logger.Error(ex, "Failed to initialize logging with configuration, using fallback file logging");
            _isInitialized = true;
        }
    }

    private LogEventLevel ParseLogLevel(string level)
    {
        return level.ToLowerInvariant() switch
        {
            "verbose" => LogEventLevel.Verbose,
            "debug" => LogEventLevel.Debug,
            "information" => LogEventLevel.Information,
            "warning" => LogEventLevel.Warning,
            "error" => LogEventLevel.Error,
            "fatal" => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }

    public ILogger GetLogger()
    {
        if (!_isInitialized)
        {
            Initialize();
        }
        return _logger ?? throw new InvalidOperationException("Logger not initialized");
    }

    public ILogger GetLogger<T>()
    {
        return GetLogger().ForContext<T>();
    }

    public ILogger GetLogger(string sourceContext)
    {
        return GetLogger().ForContext("SourceContext", sourceContext);
    }

    public void InitializeWithDefaults()
    {
        if (_isInitialized)
            return;

        var defaultConfig = new LoggingConfig
        {
            Console = new ConsoleLoggingConfig { Enabled = false },
            File = new FileLoggingConfig { Enabled = true, MinimumLevel = "Information" }
        };
        Initialize(defaultConfig);
    }

    public void Dispose()
    {
        Log.CloseAndFlush();
    }
}
