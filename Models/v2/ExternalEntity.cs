namespace HeliosETL.Models.v2;

public class ExternalEntity
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public bool Externe { get; set; }
    public SourceOfData ExternalSource { get; set; }
    public Dictionary<string, string> Data { get; set; } = new Dictionary<string, string>();
    public string DataString { get; set; } = string.Empty;
}

public class SourceOfData
{
    static long SeriaVersionUid = 2354638930571601442L;
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Kind { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime DateUpdated { get; set; } = DateTime.MinValue;
}