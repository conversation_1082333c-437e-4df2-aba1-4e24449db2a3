namespace HeliosETL.Models.v2;

public abstract class AbstractListeValeur
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Libelle { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Obsolete { get; set; } = false;
    public Dictionary<string, string> Options { get; set; } = new Dictionary<string, string>();
    public string OptionsString { get; set; } = string.Empty;
}