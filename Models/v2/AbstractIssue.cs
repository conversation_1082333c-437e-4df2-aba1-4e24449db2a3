namespace HeliosETL.Models.v2;

public class AbstractIssue
{
    public long Oid { get; set; }
    public int __version { get; set; }
    public string Code { get; set; } = String.Empty;
    public string Sujet { get; set; } = String.Empty;
    public string Description { get; set; } = String.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleDebut { get; set; } = DateTime.MinValue;
    public DateTime DatePrevisionnelleFin { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveDebut { get; set; } = DateTime.MinValue;
    public DateTime DateEffectiveFin { get; set; } = DateTime.MinValue;
    public IssuePriorite Priorite { get; set; }
    public IssueStatut Statut { get; set; }
    public AbstractActivite Activite { get; set; }
    public string KindOfActivite { get; set; } = String.Empty;
    public AbstractIssue IssueParente { get; set; }
    public string KindOfIssueParente { get; set; } = String.Empty;
    public HashSet<AbstractIssue> Issues { get; set; } = new HashSet<AbstractIssue>();
    public byte Avancement { get; set; }
    public int TempsEstimeMinutes { get; set; }
    public int TempsEffectifMinutes { get; set; }
    public HashSet<AbstractIssue> RelationsSortantes { get; set; } = new HashSet<AbstractIssue>();
    public HashSet<AbstractIssue> RelationsEntrantes { get; set; } = new HashSet<AbstractIssue>();
    public HashSet<Journal> Journaux { get; set; } = new HashSet<Journal>();
}