namespace HeliosETL.Models.v1;

public class Contacts
{
    public int Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Prenom { get; set; } = string.Empty;
    public string Civilite { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Telephone { get; set; } = string.Empty;
    public string Telephone2 { get; set; } = string.Empty;
    public string Telephone3 { get; set; } = string.Empty;
    public string Mobile { set; get; } = string.Empty;
    public string Fonction { get; set; } = string.Empty;
    public string CtNum { get; set; } = string.Empty;
    public string CtNo { get; set; } = string.Empty; 
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public string Note { get; set; } = string.Empty;
    public bool Systematique { get; set; } = false;
    public bool Facturation { get; set; } = false;
    public bool Relance { get; set; } = false;
}