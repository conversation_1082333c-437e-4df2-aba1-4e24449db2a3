namespace HeliosETL.Models.v1;

public class Tickets
{
    public int IdTickets { get; set; }
    public string CtNum { get; set; } = string.Empty;
    public string Client { get; set; } = string.Empty;
    public DateTime DateCreation { get; set; } = DateTime.MinValue;
    public string Demandeur { get; set; } = string.Empty;
    public string Assigne { get; set; } = string.Empty;
    public string Pole { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Priorite { get; set; } = string.Empty;
    public int Niveau { get; set; }
    public string Categorie { get; set; } = string.Empty;
    public string Categorie2 { get; set; } = string.Empty;
    public string Categorie3 { get; set; } = string.Empty;
    public int TempsTotal { get; set; }
    public string Titre { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DateRappel { get; set; } = DateTime.MinValue;
    public DateTime DateResolution { get; set; } = DateTime.MinValue;
    public int StatusTemps { get; set; }
    public int Avertissement { get; set; }
    public string DernierCorrespondant { get; set; } = string.Empty;
    public DateTime DatePremiereReponse { get; set; } = DateTime.MinValue;
    public DateTime DateDerniereReponse { get; set; } = DateTime.MinValue;
    public bool NotificationEnabled { get; set; } = false;
}