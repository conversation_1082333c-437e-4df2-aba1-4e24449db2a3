namespace HeliosETL.Models.v1;

public class TicketsHistorique
{
    public int IdHistorique { get; set; }
    public int IdTickets { get; set; }
    public DateTime DateModification { get; set; } = DateTime.MinValue;
    public string Correspondant { get; set; } = string.Empty;
     public string Description { get; set; } = string.Empty;
    public int NoteInterne { get; set; }
    public int PieceJointe { get; set; }
    public int EnvoiEmail { get; set; }
    public int NoteType { get; set; }
    public int Temps { get;set; }
}