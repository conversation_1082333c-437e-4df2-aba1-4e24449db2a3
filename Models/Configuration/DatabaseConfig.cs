namespace HeliosETL.Models.Configuration;

public class DatabaseConfig
{
    public SqlServerConfig SqlServer { get; set; } = new();
    public MySqlConfig MySQL { get; set; } = new();
}

public class SqlServerConfig
{
    public string DataSource { get; set; } = string.Empty;
    public string InitialCatalog { get; set; } = string.Empty;
    public string UserID { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public int Port { get; set; }
}

public class MySqlConfig
{
    public string Server { get; set; } = string.Empty;
    public string Database { get; set; } = string.Empty;
    public string UserID { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public uint Port { get; set; }
}
