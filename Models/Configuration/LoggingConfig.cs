namespace HeliosETL.Models.Configuration;

public class LoggingConfig
{
    public ConsoleLoggingConfig Console { get; set; } = new();
    public FileLoggingConfig File { get; set; } = new();
}

public class ConsoleLoggingConfig
{
    public bool Enabled { get; set; } = false;
    public string MinimumLevel { get; set; } = "Information";
}

public class FileLoggingConfig
{
    public bool Enabled { get; set; } = true;
    public string MinimumLevel { get; set; } = "Information";
    public string LogDirectory { get; set; } = "logs";
    public string FileNameTemplate { get; set; } = "helios-etl-.log";
    public int RetainedFileCountLimit { get; set; } = 30;
    public long FileSizeLimitBytes { get; set; } = 10485760; // 10MB
}
