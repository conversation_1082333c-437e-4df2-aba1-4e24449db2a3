# HeliosETL

## Présentation
HeliosETL est une application C# conçue pour automatiser l'extraction, la transformation et le chargement (ETL) des données. Elle se base sur une architecture modulaire permettant de configurer facilement les différents composants pour une variété de sources et de destinations de données.

## Fonctionnalités principales
- Extraction de données depuis diverses sources
- Tests de connexion et initialisation automatiques
- Transformation configurable des données
- Chargement des données dans une base cible
- Logging détaillé des opérations via Serilog
- Interface console enrichie avec Spectre.Console

## Structure du projet
- `Program.cs` : Point d’entrée de l’application, gestion de l’orchestration générale
- `Services/` : Services de configuration, extraction, transformation, chargement, validation et logging
- `Models/` : Définition des modèles de données et configurations
- `config.example.yaml` : Exemple de fichier de configuration du projet
- `docs/` : Documentation complémentaire (configuration, logging...)

## Mise en route
1. **Clonage du projet** :
    ```bash
    git clone <repository_url>
    ```
2. **Configuration** :
    - Copier `config.example.yaml` vers `config.yaml` et adapter selon votre environnement
3. **Compilation** :
    Utiliser Visual Studio ou la commande suivante :
    ```bash
    dotnet build HeliosETL.sln
    ```
4. **Exécution** :
    ```bash
    dotnet run --project HeliosETL.csproj
    ```

## Dépendances principales
- [.NET](https://dotnet.microsoft.com/) 6.0 ou supérieur
- [Serilog](https://serilog.net/)
- [Spectre.Console](https://spectreconsole.net/)

## Documentation supplémentaire
Retrouvez des informations complémentaires dans le dossier `docs/` :
- Configuration : `docs/README-Configuration.md`
- Logging : `docs/README-Logging.md`

## Auteurs
- Équipe AB