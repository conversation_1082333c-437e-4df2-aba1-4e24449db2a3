# HeliosETL Configuration Service

This document explains how to use the configuration service in HeliosETL.

## Overview

The configuration service manages global variables and settings for the HeliosETL application, particularly database connection information. It uses YAML files for configuration storage.

## Setup

1. **Copy the example configuration file:**
   ```bash
   copy config.example.yaml config.yaml
   ```

2. **Edit the configuration file:**
   Open `config.yaml` and fill in your actual configuration:
   ```yaml
   database:
     sqlServer:
       dataSource: "your-sql-server-host"
       initialCatalog: "your-database-name"
       userID: "your-username"
       password: "your-password"

     mySQL:
       server: "your-mysql-server-host"
       database: "your-mysql-database-name"
       userID: "your-mysql-username"
       password: "your-mysql-password"

   logging:
     console:
       enabled: false               # Console logging disabled - using Spectre.Console
       minimumLevel: "Information"
     file:
       enabled: true
       minimumLevel: "Information"  # All levels to files (change to "Error" for error-only)
       logDirectory: "logs"
       fileNameTemplate: "helios-etl-{Date}.log"
       retainedFileCountLimit: 30
       fileSizeLimitBytes: 10485760
   ```

## Usage

### Accessing Configuration in Code

```csharp
// Get the configuration service instance
var configService = ConfigurationService.Instance;

// Get the complete configuration
var config = configService.GetConfiguration();

// Get only database configuration
var dbConfig = configService.GetDatabaseConfig();

// Get logging configuration
var loggingConfig = configService.GetConfiguration().Logging;

// Access specific values
string sqlServerHost = dbConfig.SqlServer.DataSource;
string mysqlDatabase = dbConfig.MySQL.Database;
```

### Reloading Configuration

If you need to reload the configuration at runtime:

```csharp
ConfigurationService.Instance.ReloadConfiguration();
```

## File Structure

- `config.yaml` - Your actual configuration file (gitignored for security)
- `config.example.yaml` - Example configuration file (committed to repository)
- `Models/Configuration/` - Configuration model classes
- `Services/ConfigurationService.cs` - Configuration service implementation
- `Services/LoggingService.cs` - Logging service implementation
- `logs/` - Log files directory (gitignored)