# HeliosETL Logging Service

This document explains how to use the logging service in HeliosETL.

## Overview

The logging service provides comprehensive logging capabilities for the HeliosETL application using Serilog. It supports both console and file logging with configurable log levels.

## Features

- **File-Only Logging**: All logging goes to files, terminal uses Spectre.Console for display
- **Configurable Log Levels**: Control what gets logged to files
- **Configurable**: All settings managed through YAML configuration
- **Singleton Pattern**: Global access similar to ConfigurationService
- **Automatic Log Rotation**: Daily rotation with size limits
- **Log Retention**: Configurable retention period (30 days by default)

## Configuration

Logging is configured in the `config.yaml` file:

```yaml
logging:
  console:
    enabled: false               # Console logging disabled - using Spectre.Console for display
    minimumLevel: "Information"  # Verbose, Debug, Information, Warning, Error, Fatal

  file:
    enabled: true
    minimumLevel: "Information"  # All log levels to files (change to "Error" for error-only)
    logDirectory: "logs"
    fileNameTemplate: "helios-etl-{Date}.log"
    retainedFileCountLimit: 30   # Keep 30 days of logs
    fileSizeLimitBytes: 10485760 # 10MB per file
```

## Usage

### Basic Logging

```csharp
// Get a logger for your class
var logger = LoggingService.Instance.GetLogger<YourClass>();

// Or get a logger with a custom name
var logger = LoggingService.Instance.GetLogger("CustomName");

// Log messages at different levels
logger.Verbose("Detailed tracing information");
logger.Debug("Debug information");
logger.Information("General information");
logger.Warning("Warning message");
logger.Error("Error occurred");
logger.Fatal("Fatal error");
```

### Logging with Exception Details

```csharp
try
{
    // Some operation
}
catch (Exception ex)
{
    logger.Error(ex, "Operation failed with error");
}
```

### Structured Logging

```csharp
logger.Information("User {UserId} performed {Action} at {Timestamp}", 
    userId, action, DateTime.Now);
```

## Log Levels

- **Verbose**: Most detailed level, typically only enabled during development
- **Debug**: Debug information, useful for troubleshooting
- **Information**: General information about application flow
- **Warning**: Potentially harmful situations
- **Error**: Error events that might still allow the application to continue
- **Fatal**: Very severe error events that might cause the application to terminate

## File Logging Details

- **Location**: Logs are stored in the `logs/` directory by default
- **Format**: `helios-etl-YYYY-MM-DD.log`
- **Content**: All log levels (configurable via `minimumLevel`)
- **Rotation**: New file created daily
- **Size Limit**: Files are rotated when they exceed 10MB
- **Retention**: Old log files are automatically deleted after 30 days

## Terminal Display

- **Spectre.Console**: All terminal output uses Spectre.Console for rich formatting
- **No Log Pollution**: Logging doesn't interfere with Spectre.Console display
- **Clean Interface**: Terminal shows only user-friendly messages via Spectre

## Integration with Existing Code

The logging service is integrated with:

- **ConfigurationService**: Logs configuration loading events
- **Database**: Logs connection setup and testing
- **Program.cs**: Logs application lifecycle events

## Best Practices

1. **Use appropriate log levels**: Don't log everything as Information
2. **Include context**: Use structured logging with relevant parameters
3. **Log exceptions**: Always include the exception object when logging errors
4. **Avoid sensitive data**: Don't log passwords or other sensitive information
5. **Use class-specific loggers**: Get loggers for each class for better traceability
6. **Separate concerns**: Use Spectre.Console for user display, logging for debugging/auditing

## Example Implementation

```csharp
public class MyService
{
    private readonly ILogger _logger;
    
    public MyService()
    {
        _logger = LoggingService.Instance.GetLogger<MyService>();
    }
    
    public bool ProcessData(string data)
    {
        _logger.Information("Starting data processing for {DataLength} characters", data.Length);
        
        try
        {
            // Process data
            _logger.Debug("Data processing completed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to process data");
            return false;
        }
    }
}
```

## Troubleshooting

- **No logs appearing**: Check if logging is enabled in configuration
- **File logs not created**: Verify the application has write permissions to the log directory
- **Too many log files**: Adjust `retainedFileCountLimit` in configuration
- **Large log files**: Reduce `fileSizeLimitBytes` or adjust log levels
