# Rapport de Structure et de Logique du Projet HeliosETL

## Table des Matières
1. [Aperçu du Projet](#aperçu-du-projet)
2. [Structure du Projet](#structure-du-projet)
3. [Modèles de Données](#modèles-de-données)
4. [Architecture des Services](#architecture-des-services)
5. [Architecture de la Base de Données](#architecture-de-la-base-de-données)
6. [Système de Configuration](#système-de-configuration)
7. [Système de Journalisation](#système-de-journalisation)
8. [Conception du Pipeline ETL](#conception-du-pipeline-etl)
9. [État d'Implémentation](#état-dimplémentation)
10. [Directives de Développement](#directives-de-développement)

## Aperçu du Projet

**HeliosETL** est une application console C# conçue pour automatiser le processus d'Extraction, de Transformation et de Chargement (ETL) pour la migration des données de Helios v1 vers Helios v2. L'application suit une architecture modulaire avec une séparation claire des préoccupations et implémente des modèles C# modernes.

### Fonctionnalités Clés
- **Pipeline ETL Modulaire** : Extracteurs, transformateurs et chargeurs séparés
- **Support de Doubles Bases de Données** : SQL Server (source) et MySQL (destination)
- **Interface Console Riche** : Spectre.Console pour une sortie terminal conviviale
- **Journalisation Complète** : Journalisation de fichiers basée sur Serilog avec niveaux configurables
- **Configuration YAML** : Gestion de configuration flexible
- **Modèle de Référentiel** : Abstraction propre de la couche d'accès aux données
- **Services Singleton** : Gestion centralisée des services

### Stack Technologique
- **.NET 9.0** : Framework .NET le plus récent
- **Serilog** : Journalisation structurée
- **Spectre.Console** : Interface terminal riche
- **YamlDotNet** : Analyse de configuration YAML
- **Microsoft.Data.SqlClient** : Connectivité SQL Server
- **MySql.Data** : Connectivité MySQL

## Structure du Projet

```
HeliosETL/
├── Const/                          # Application constants
│   └── Error.cs                    # Error message constants
├── Models/                         # Data models and DTOs
│   ├── Configuration/              # Configuration models
│   │   ├── AppConfig.cs           # Main application configuration
│   │   ├── DatabaseConfig.cs      # Database connection settings
│   │   └── LoggingConfig.cs       # Logging configuration
│   ├── v1/                        # Helios v1 data models (source)
│   │   ├── Tickets.cs             # Ticket entity
│   │   ├── TicketsHistorique.cs   # Ticket history entity
│   │   └── Categorie.cs           # Category entity
│   ├── ETLHistory.cs              # ETL tracking and history model
│   └── v2/                        # Helios v2 data models (destination)
│       ├── AbstractListeValeur.cs # Base class for reference data
│       ├── Activite.cs            # Abstract activity entity
│       ├── Commanditaire.cs       # Client/sponsor entity
│       ├── DomaineMetier.cs       # Business domain entity
│       ├── ExternalEntity.cs      # Base class for external entities
│       ├── Issue.cs               # Issue entity (maps from Tickets)
│       ├── IssueOrigine.cs        # Issue origin entity
│       ├── IssuePieceJointe.cs    # Issue attachment entity
│       ├── IssuePriorite.cs       # Issue priority entity
│       ├── IssueStatut.cs         # Issue status entity
│       ├── Journal.cs             # Journal entity (maps from TicketsHistorique)
│       ├── JournalDetails.cs      # Extended journal details
│       ├── Mission.cs             # Mission entity (extends Activite)
│       ├── NiveauComplexite.cs    # Complexity level entity
│       ├── Personne.cs            # Person entity
│       ├── Statut.cs              # General status enumeration
│       ├── TypeDocument.cs        # Document type entity
│       ├── TypeMission.cs         # Mission type entity
│       ├── TypePersonne.cs        # Person type entity
│       ├── TypeProjet.cs          # Project type entity
│       └── TypeRelationIssue.cs   # Issue relationship type enumeration
├── Repositories/                   # Data access layer
│   ├── IRepository.cs             # Generic repository interface
│   ├── Repository.cs              # Base repository implementation
│   ├── ETLHistoryRepository.cs    # ETL history tracking (SQLite)
│   ├── v1/                        # Helios v1 repositories (SQL Server)
│   │   ├── TicketsRepository.cs   # Tickets data access
│   │   ├── TicketHistoriqueRepository.cs # Ticket history data access
│   │   └── CategorieRepository.cs # Category data access
│   └── v2/                        # Helios v2 repositories (MySQL)
│       ├── IssueRepository.cs     # Issues data access
│       ├── JournalRepository.cs   # Journal data access
│       ├── IssuePieceJointeRepository.cs # Issue attachments data access
│       ├── IssuePrioriteRepository.cs # Issue priority data access
│       ├── MissionRepository.cs   # Mission data access
│       ├── TypeMissionRepository.cs # Mission type data access
│       ├── DomaineMetierRepository.cs # Business domain data access
│       ├── PersonneRepository.cs  # Person data access
│       └── TypePersonneRepository.cs # Person type data access
├── Services/                      # Business logic and utilities
│   ├── ConfigurationService.cs   # Configuration management
│   ├── LoggingService.cs          # Logging service
│   ├── Database.cs                # Database connection management
│   ├── CachedMemory.cs            # In-memory data cache
│   ├── Extractors/                # Data extraction services
│   │   ├── IExtractor.cs          # Extractor interface
│   │   ├── TicketsExtractor.cs    # Tickets extraction logic
│   │   ├── TicketHistoryExtractor.cs # History extraction logic
│   │   └── PoleExtractor.cs       # Pole extraction logic
│   ├── Transformers/              # Data transformation services
│   │   ├── ITransformer.cs        # Transformer interface
│   │   ├── TicketToIssueTransformer.cs # Ticket→Issue transformation
│   │   ├── TicketHistoryToJournalTransformer.cs # History→Journal transformation
│   │   ├── CategoriesToMissionTransformer.cs # Category→Mission transformation
│   │   └── PoleToDomaineMetierTransformer.cs # Pole→Domain transformation
│   ├── Loaders/                   # Data loading services
│   │   ├── IssueLoader.cs         # Issue loading logic
│   │   ├── JournalLoader.cs       # Journal loading logic
│   │   ├── MissionLoader.cs       # Mission loading logic
│   │   └── DomaineMetierLoader.cs # Domain loading logic
│   └── Validators/                # Data validation services
│       ├── IValidator.cs          # Validator interface
│       ├── TicketValidator.cs     # Ticket validation logic
│       ├── IssueValidator.cs      # Issue validation logic
│       ├── JournalValidator.cs    # Journal validation logic
│       ├── MissionValidator.cs    # Mission validation logic
│       ├── DomaineMetierValidator.cs # Domain validation logic
│       └── TicketHistoryValidator.cs # History validation logic
├── Utils/                         # Utility classes and helpers
│   └── EnumUtils.cs               # Enum extension methods
├── docs/                          # Documentation
│   ├── README-Configuration.md    # Configuration guide
│   ├── README-Logging.md          # Logging guide
│   └── PROJECT-STRUCTURE-REPORT.md # This report
├── config.example.yaml            # Configuration template
├── config.yaml                    # Actual configuration (gitignored)
├── Program.cs                     # Application entry point
├── HeliosETL.csproj              # Project file
└── HeliosETL.sln                 # Solution file
```

## Modèles de Données

### Modèles Helios v1 (Système Source)
Les modèles v1 représentent la structure du système hérité :

**Tickets** - Entité principale de ticket avec 30 propriétés incluant :
- Informations de base : `idTickets`, `ct_num`, `client`, `dateCreation`
- Assignation : `demandeur`, `assigne`, `pole`
- Classification : `type`, `status`, `priorite`, `niveau`
- Catégories : `categorie`, `categorie2`, `categorie3`
- Chronométrage : `tempsTotal`, `dateRappel`, `dateResolution`
- Contenu : `titre`, `description`

**TicketsHistorique** - Entrées d'historique/journal des tickets :
- Liens vers les tickets via `idTickets`
- Suivi des modifications avec `datModificationn`, `correspondant`
- Contient `description`, `temps` (temps passé)
- Indicateurs : `noteInterne`, `pieceJointe`, `envoiEmail`

**Categorie** - Structure simple de catégorie :
- `libelle` (étiquette) et `description`

### Modèles Helios v2 (Système de Destination)
Les modèles v2 représentent le système modernisé :

**Issue** - Représentation améliorée du ticket :
- Identité basée sur OID avec versionnement (`oid`, `__version`)
- Métadonnées riches : `code`, `sujet`, `description`
- Datation complète : création, modification, début/fin planifiés/effectifs
- Énumérations de priorité et de statut
- Relations hiérarchiques (problèmes parent/enfant)
- Suivi du temps : minutes estimées vs. effectives
- Relations avec les activités et les journaux

**Journal** - Suivi d'historique modernisé :
- Liens vers les problèmes, prend en charge les notes privées/internes
- Structure simplifiée axée sur les données essentielles

**DomaineMetier** - Classification des domaines d'activité :
- Remplace le concept de pôle de v1
- Prend en charge le suivi d'obsolescence

**Activite** - Gestion d'activité/projet :
- Liens vers les domaines d'activité
- Suivi complet du cycle de vie du projet

**Mission** & **TypeMission** - Gestion de mission :
- Structure de mission hiérarchique
- Classification basée sur le type avec associations de domaine

**AbstractListeValeur** - Classe de base pour les données de référence :
- Fournit une structure commune pour tous les types de listes de valeurs
- Propriétés communes : `oid`, `__version`, `code`, `libelle`, `description`, `obsolete`
- Support des données étendues via dictionnaire `data`

**ExternalEntity** - Classe de base pour les entités externes :
- Support des entités provenant de sources externes
- Propriétés : `oid`, `__version`, `externe`, `externalSource`
- Dictionnaire de données flexible pour l'intégration

**Personne** & **TypePersonne** - Gestion des personnes :
- Informations complètes : nom, prénom, fonction, contacts
- Classification par type avec relations vers les domaines métier
- Support des personnes internes et externes

**Commanditaire** - Gestion des clients/sponsors :
- Informations de contact : nom, email, téléphone
- Relations avec les personnes associées
- Hérite d'ExternalEntity pour l'intégration

**IssuePieceJointe** - Gestion des pièces jointes :
- Support des fichiers avec métadonnées : nom, type MIME, taille
- Relations vers Issues et Journaux
- Suivi de la date de création

**IssuePriorite** & **IssueStatut** - Classification des Issues :
- Priorités avec grades numériques et relations aux domaines
- Statuts avec indicateurs nouveau/fermé
- Héritent d'AbstractListeValeur

**NiveauComplexite** & **TypeDocument** - Types de référence :
- Classification de la complexité des tâches
- Types de documents avec relations aux domaines métier
- Support de l'obsolescence et du versionnement

**Énumérations** :
- `Statut` - États généraux (brouillon, actif, en_attente, terminé, archivé, annulé)
- `TypeRelationIssue` - Types de relations entre Issues (bloque, duplique, suit, etc.)
- `ETLState` - États du processus ETL (Failed, InProcess, Success)

**ETLHistory** - Suivi des opérations ETL :
- Traçabilité des transformations avec anciens/nouveaux IDs
- Suivi des états et messages d'erreur
- Horodatage des opérations

## Architecture des Services

### Services de Base

**ConfigurationService** (Singleton)
- Gestion de configuration basée sur YAML
- Implémentation singleton thread-safe
- Rechargement automatique de la configuration
- Modèles de configuration fortement typés

**LoggingService** (Singleton)
- Journalisation structurée basée sur Serilog
- Journalisation uniquement par fichier (le terminal utilise Spectre.Console)
- Niveaux de journalisation et rétention configurables
- Journalisation de secours pour les échecs de configuration

**Database**
- Gestion de connexion à double base de données (SQL Server + MySQL)
- Génération de chaîne de connexion à partir de la configuration
- Test et validation de connexion
- Gestion appropriée du cycle de vie de connexion

**CachedMemory** (Singleton)
- Cache de données en mémoire pour les opérations ETL
- Collections séparées pour les entités v1 et v2
- Optimise l'accès aux données pendant la transformation

### Composants ETL

**Extracteurs** (`IExtractor`)
- Interface d'extraction générique avec trois méthodes :
  - `Extract<T>()` - Extraire toutes les entités
  - `Extract<T>(string query)` - Extraction basée sur requête
  - `Extract<T>(int id)` - Extraction d'entité unique
- Implémentations : `TicketsExtractor`, `TicketHistoryExtractor`, `PoleExtractor`

**Transformateurs** (`ITransformer`)
- Processus de transformation en cinq phases :
  - `Extract<T>()` - Extraction de données
  - `Prepare<T>()` - Préparation de données
  - `Transform<T>()` - Transformation de données
  - `Validate()` - Validation de données
  - `Load()` - Chargement de données
- Transformateurs clés :
  - `TicketToIssueTransformer` - Migration de ticket principale
  - `TicketHistoryToJournalTransformer` - Migration d'historique
  - `CategoriesToMissionTransformer` - Mappage de catégorie à mission
  - `PoleToDomaineMetierTransformer` - Mappage de pôle à domaine

**Chargeurs**
- Logique de chargement spécialisée pour chaque entité v2
- Gestion de l'insertion de données spécifique à MySQL
- Gestion des relations et des clés étrangères

**Validateurs**
- Assurance qualité des données avant chargement
- Validation des règles métier
- Vérifications d'intégrité référentielle

### Utilitaires

**EnumUtils** - Extensions pour les énumérations :
- Méthode d'extension `GetDescription()` pour extraire les attributs Description
- Support des énumérations avec attributs `[Description]`
- Utilisé pour les énumérations comme `Statut` et `TypeRelationIssue`

## Architecture de la Base de Données

### Implémentation du Modèle de Référentiel

**IRepository** - Interface de référentiel générique :
```csharp
public interface IRepository
{
    HashSet<T> GetAll<T>();
    T GetById<T>(int id);
    bool Add<T>(T entity);
    bool Update<T>(T entity);
    bool Delete<T>(T entity);
    bool DeleteById<T>(int id);
}
```

**Repository** - Classe de référentiel de base :
- Gère l'initialisation de connexion à la base de données
- Fournit l'infrastructure de journalisation
- Traite les préoccupations communes de référentiel

**Référentiels Spécialisés** :

**v1 (SQL Server)** :
- `TicketsRepository` - Opérations SQL Server pour les tickets v1
- `TicketHistoriqueRepository` - Opérations SQL Server pour l'historique v1
- `CategorieRepository` - Accès aux données de catégorie

**v2 (MySQL)** :
- `IssueRepository` - Opérations MySQL pour les problèmes v2
- `JournalRepository` - Opérations MySQL pour les journaux v2
- `IssuePieceJointeRepository` - Opérations MySQL pour les pièces jointes
- `IssuePrioriteRepository` - Opérations MySQL pour les priorités d'issues
- `MissionRepository` - Opérations MySQL pour les missions
- `TypeMissionRepository` - Opérations MySQL pour les types de mission
- `DomaineMetierRepository` - Opérations MySQL pour les domaines métier
- `PersonneRepository` - Opérations MySQL pour les personnes
- `TypePersonneRepository` - Opérations MySQL pour les types de personne

**Autres** :
- `ETLHistoryRepository` - Opérations SQLite pour l'historique ETL

### Connexions de Base de Données

**SQL Server (Source)**
- Se connecte à la base de données Helios v1
- Opérations en lecture seule pour l'extraction de données
- Chaîne de connexion : `Data Source={host};Initial Catalog={db};User ID={user};Password=******;TrustServerCertificate=true`

**MySQL (Destination)**
- Se connecte à la base de données Helios v2
- Opérations d'écriture pour le chargement de données
- Chaîne de connexion : `Server={host};Database={db};Uid={user};Pwd=******;`

## Système de Configuration

### Configuration Basée sur YAML
L'application utilise un système de configuration YAML hiérarchique :

```yaml
database:
  sqlServer:
    dataSource: "server-host"
    initialCatalog: "database-name"
    userID: "username"
    password: "password"
  mySQL:
    server: "mysql-host"
    database: "database-name"
    userID: "username"
    password: "password"

logging:
  console:
    enabled: false
    minimumLevel: "Information"
  file:
    enabled: true
    minimumLevel: "Information"
    logDirectory: "logs"
    fileNameTemplate: "helios-etl-{Date}.log"
    retainedFileCountLimit: 30
    fileSizeLimitBytes: 10485760
```

### Modèles de Configuration
- **AppConfig** - Conteneur de configuration racine
- **DatabaseConfig** - Paramètres de connexion à la base de données
- **LoggingConfig** - Configuration de journalisation avec paramètres console et fichier
- **SqlServerConfig** / **MySqlConfig** - Paramètres spécifiques à la base de données

## Système de Journalisation

### Intégration Serilog
- **Journalisation Fichier Uniquement** : Tous les logs vont aux fichiers, le terminal utilise Spectre.Console
- **Journalisation Structurée** : Support pour les messages de log paramétrés
- **Niveaux de Log** : Verbose, Debug, Information, Warning, Error, Fatal
- **Rotation Automatique** : Rotation quotidienne avec limites de taille (10Mo par défaut)
- **Rétention** : Période de rétention configurable (30 jours par défaut)

### Modèles d'Utilisation
```csharp
private readonly ILogger _logger = LoggingService.Instance.GetLogger<MyClass>();

_logger.Information("Traitement de {RecordCount} enregistrements", count);
_logger.Error(ex, "Échec du traitement de l'enregistrement {RecordId}", id);
```

## Conception du Pipeline ETL

### Flux de Migration de Données
Exemples:
1. **Tickets → Issues**
   - Extraction des tickets depuis SQL Server
   - Transformation des propriétés de ticket au format problème
   - Mappage des catégories aux activités/missions
   - Gestion des mappages de statut et de priorité
   - Chargement dans la table Issues MySQL

2. **TicketsHistorique → Journal**
   - Extraction de l'historique des tickets depuis SQL Server
   - Transformation au format journal
   - Liaison aux problèmes correspondants
   - Préservation des informations de chronométrage et de notes
   - Chargement dans la table Journal MySQL

3. **Categories → Missions**
   - Extraction des hiérarchies de catégories
   - Transformation en structure de mission
   - Création de types de mission et de relations
   - Chargement dans les tables Mission MySQL

4. **Poles → DomaineMetier**
   - Extraction des informations de pôle
   - Transformation au format domaine métier
   - Chargement dans la table DomaineMetier MySQL

### Logique de Transformation

Le processus de transformation principal (tel que documenté dans `TicketToIssueTransformer`) :

1. **Extraire** tous les tickets et l'historique des tickets
2. **Valider** l'intégrité des données source
3. **Transformer** les données au format v2 :
   - Entités Issue à partir des tickets
   - Entrées de journal à partir de l'historique
   - DomaineMetier à partir des pôles
   - Entités Activite
   - Structures de Mission
4. **Charger** les données transformées dans MySQL

## État d'Implémentation

### ✅ Composants Terminés

**Infrastructure** :
- Service de configuration avec support YAML
- Service de journalisation avec intégration Serilog
- Gestion de connexion à la base de données (SQL Server, MySQL, SQLite)
- Implémentation du modèle de référentiel
- Gestion des erreurs et constantes
- Utilitaires pour les énumérations

**Modèles de Données** :
- Définitions complètes des modèles v1 (Tickets, TicketsHistorique, Categorie)
- Modèles v2 complets avec hiérarchie d'héritage :
  - Entités principales : Issue, Journal, Mission, Activite
  - Entités de référence : DomaineMetier, TypeMission, IssuePriorite, IssueStatut
  - Entités de personnes : Personne, TypePersonne, Commanditaire
  - Entités de support : IssuePieceJointe, ExternalEntity, AbstractListeValeur
  - Types de référence : NiveauComplexite, TypeDocument, TypeProjet
- Modèles de configuration (AppConfig, DatabaseConfig, LoggingConfig)
- Énumérations avec descriptions (Statut, TypeRelationIssue, ETLState)
- Modèle de suivi ETL (ETLHistory)

**Services de Base** :
- ConfigurationService (entièrement implémenté)
- LoggingService (entièrement implémenté)
- Service Database (entièrement implémenté)
- Service CachedMemory (structure complète)

**Référentiels** :
- Interface IRepository
- Classe Repository de base
- ETLHistoryRepository (opérations CRUD implémentées)

**v1 Repositories** :
- TicketsRepository (opérations CRUD implémentées)
- TicketHistoriqueRepository (opérations CRUD implémentées)
- CategorieRepository (opérations CRUD implémentées)

**v2 Repositories** :
- IssueRepository (opérations CRUD implémentées)
- JournalRepository (opérations CRUD implémentées)
- IssuePieceJointeRepository (opérations CRUD implémentées)
- IssuePrioriteRepository (opérations CRUD implémentées)
- MissionRepository (opérations CRUD implémentées)
- TypeMissionRepository (opérations CRUD implémentées)
- DomaineMetierRepository (opérations CRUD implémentées)
- PersonneRepository (opérations CRUD implémentées)
- TypePersonneRepository (opérations CRUD implémentées)

### 🚧 Partiellement Implémentés

**Composants ETL** :
- **Interfaces complètes** :
  - `IExtractor` - Interface d'extraction avec méthodes Extract<T>()
  - `ITransformer` - Interface de transformation avec Extract, Prepare, Transform, Validate, Load
  - `IValidator` - Interface de validation (structure de base)
- **Extracteurs** (structures créées, implémentations à compléter) :
  - `TicketsExtractor` - Extraction des tickets depuis SQL Server
  - `TicketHistoryExtractor` - Extraction de l'historique des tickets
  - `PoleExtractor` - Extraction des données de pôles
- **Transformateurs** (structures créées, implémentations à compléter) :
  - `TicketToIssueTransformer` - Transformation Tickets → Issues
  - `TicketHistoryToJournalTransformer` - Transformation TicketsHistorique → Journal
  - `CategoriesToMissionTransformer` - Transformation Catégories → Missions
  - `PoleToDomaineMetierTransformer` - Transformation Pôles → DomaineMetier
- **Chargeurs** (structures créées, implémentations vides) :
  - `IssueLoader`, `JournalLoader`, `MissionLoader`, `DomaineMetierLoader`
- **Validateurs** (structures créées, implémentations vides) :
  - `IssueValidator`, `JournalValidator`, `MissionValidator`, `DomaineMetierValidator`
  - `TicketValidator`, `TicketHistoryValidator`

### ❌ Pas Encore Implémentés

**Logique ETL Principale** :
- Implémentations d'extraction réelles
- Algorithmes de transformation de données
- Procédures de chargement
- Règles de validation
- Orchestration ETL principale dans `Program.cs` (la méthode `Run()` retourne false)

**Fonctionnalités Supplémentaires** :
- Mécanismes de récupération d'erreur
- Rapports de progression
- Surveillance des performances
- Vérification de migration de données

### 📊 Statistiques du Projet

**Modèles** : 25+ classes de modèles (v1: 3, v2: 20+, Configuration: 3, ETL: 1)
**Référentiels** : 12 référentiels complets (v1: 3, v2: 9)
**Services** : 4 services de base + 15+ composants ETL
**Interfaces** : 4 interfaces principales (IRepository, IExtractor, ITransformer, IValidator)
**Énumérations** : 3 énumérations avec descriptions
**Utilitaires** : 1 classe d'extension pour les énumérations

### 🎯 Prochaines Étapes Recommandées

1. **Implémentation ETL** : Compléter les extracteurs, transformateurs et chargeurs
2. **Tests Unitaires** : Créer des tests pour tous les nouveaux référentiels
3. **Validation** : Implémenter la logique de validation métier
4. **Orchestration** : Développer le processus ETL principal dans Program.cs
5. **Performance** : Optimiser les requêtes et ajouter la mise en cache
6. **Monitoring** : Ajouter le suivi des performances et la gestion d'erreurs