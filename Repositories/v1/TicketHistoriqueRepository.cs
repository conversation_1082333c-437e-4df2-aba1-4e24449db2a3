using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class TicketHistoriqueRepository : Repository<TicketsHistorique>
    {
        public TicketHistoriqueRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<TicketsHistorique> GetAll()
        {
            var ticketsHistorique = new List<TicketsHistorique>();
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM TicketsHistorique", (SqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        ticketsHistorique.Add(new TicketsHistorique
                        {
                            IdHistorique = GetSafeInt(reader, "idHistorique"),
                            IdTickets = GetSafeInt(reader, "idTickets"),
                            DateModification = GetSafeDateTime(reader, "dateModification"),
                            Correspondant = GetSafeString(reader, "correspondant"),
                            Description = GetSafeString(reader, "description"),
                            NoteInterne = GetSafeInt(reader, "noteInterne"),
                            PieceJointe = GetSafeInt(reader, "pieceJointe"),
                            EnvoiEmail = GetSafeInt(reader, "envoiEmail"),
                            NoteType = GetSafeInt(reader, "noteType"),
                            Temps = GetSafeInt(reader, "temps")
                        });
                    }
                }
            }
            CloseConnection();
            return ticketsHistorique;
        }

        public override TicketsHistorique GetById(int id)
        {
            TicketsHistorique historique = null;
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM TicketsHistorique WHERE idHistorique = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        historique = new TicketsHistorique
                        {
                            IdHistorique = GetSafeInt(reader, "idHistorique"),
                            IdTickets = GetSafeInt(reader, "idTickets"),
                            DateModification = GetSafeDateTime(reader, "dateModification"),
                            Correspondant = GetSafeString(reader, "correspondant"),
                            Description = GetSafeString(reader, "description"),
                            NoteInterne = GetSafeInt(reader, "noteInterne"),
                            PieceJointe = GetSafeInt(reader, "pieceJointe"),
                            EnvoiEmail = GetSafeInt(reader, "envoiEmail"),
                            NoteType = GetSafeInt(reader, "noteType"),
                            Temps = GetSafeInt(reader, "temps")
                        };
                    }
                }
            }
            CloseConnection();
            return historique;
        }

        public override void Add(TicketsHistorique entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("INSERT INTO TicketsHistorique (idTickets, dateModification, correspondant, description, noteInterne, pieceJointe, envoiEmail, noteType, temps) VALUES (@idTickets, @dateModification, @correspondant, @description, @noteInterne, @pieceJointe, @envoiEmail, @noteType, @temps)", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@idTickets", entity.IdTickets);
                command.Parameters.AddWithValue("@dateModification", entity.DateModification);
                command.Parameters.AddWithValue("@correspondant", entity.Correspondant);
                command.Parameters.AddWithValue("@description", entity.Description);
                command.Parameters.AddWithValue("@noteInterne", entity.NoteInterne);
                command.Parameters.AddWithValue("@pieceJointe", entity.PieceJointe);
                command.Parameters.AddWithValue("@envoiEmail", entity.EnvoiEmail);
                command.Parameters.AddWithValue("@noteType", entity.NoteType);
                command.Parameters.AddWithValue("@temps", entity.Temps);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(TicketsHistorique entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("UPDATE TicketsHistorique SET idTickets = @idTickets, dateModification = @dateModification, correspondant = @correspondant, description = @description, noteInterne = @noteInterne, pieceJointe = @pieceJointe, envoiEmail = @envoiEmail, noteType = @noteType, temps = @temps WHERE idHistorique = @idHistorique", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@idTickets", entity.IdTickets);
                command.Parameters.AddWithValue("@dateModification", entity.DateModification);
                command.Parameters.AddWithValue("@correspondant", entity.Correspondant);
                command.Parameters.AddWithValue("@description", entity.Description);
                command.Parameters.AddWithValue("@noteInterne", entity.NoteInterne);
                command.Parameters.AddWithValue("@pieceJointe", entity.PieceJointe);
                command.Parameters.AddWithValue("@envoiEmail", entity.EnvoiEmail);
                command.Parameters.AddWithValue("@noteType", entity.NoteType);
                command.Parameters.AddWithValue("@temps", entity.Temps);
                command.Parameters.AddWithValue("@idHistorique", entity.IdHistorique);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(TicketsHistorique entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("DELETE FROM TicketsHistorique WHERE idHistorique = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", entity.IdHistorique);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}