using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class CategorieRepository : Repository<Categorie>
    {
        public CategorieRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Categorie> GetAll()
        {
            var categories = new List<Categorie>();
            OpenConnection();
            using (var command = new SqlCommand("SELECT libelle, idPole FROM Categorie", (SqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        categories.Add(new Categorie
                        {
                            Libelle = GetSafeString(reader, "libelle"),
                            IdPole = GetSafeInt(reader, "idPole")
                        });
                    }
                }
            }
            CloseConnection();
            return categories;
        }

        public override Categorie GetById(int id)
        {
            Categorie categorie = null;
            OpenConnection();
            using (var command = new SqlCommand("SELECT libelle, idPole FROM Categorie WHERE idPole = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        categorie = new Categorie
                        {
                            Libelle = GetSafeString(reader, "libelle"),
                            IdPole = GetSafeInt(reader, "idPole")
                        };
                    }
                }
            }
            CloseConnection();
            return categorie;
        }

        public override void Add(Categorie entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("INSERT INTO Categorie (libelle, idPole) VALUES (@libelle, @idPole)", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@libelle", entity.Libelle);
                command.Parameters.AddWithValue("@idPole", entity.IdPole);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Categorie entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("UPDATE Categorie SET libelle = @libelle WHERE idPole = @idPole", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@libelle", entity.Libelle);
                command.Parameters.AddWithValue("@idPole", entity.IdPole);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Categorie entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("DELETE FROM Categorie WHERE idPole = @idPole", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@idPole", entity.IdPole);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}