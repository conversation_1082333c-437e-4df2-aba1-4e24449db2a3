using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class TicketsRepository : Repository<Tickets>
    {
        public TicketsRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Tickets> GetAll()
        {
            var tickets = new List<Tickets>();
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM Tickets", (SqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        tickets.Add(new Tickets
                        {
                            IdTickets = GetSafeInt(reader, "idTickets"),
                            CtNum = GetSafeString(reader, "ct_num"),
                            Client = GetSafeString(reader, "client"),
                            DateCreation = GetSafeDateTime(reader, "dateCreation"),
                            Demandeur = GetSafeString(reader, "demandeur"),
                            Assigne = GetSafeString(reader, "assigne"),
                            Pole = GetSafeString(reader, "pole"),
                            Type = GetSafeString(reader, "type"),
                            Status = GetSafeString(reader, "status"),
                            Priorite = GetSafeString(reader, "priorite"),
                            Niveau = GetSafeInt(reader, "niveau"),
                            Categorie = GetSafeString(reader, "categorie"),
                            Categorie2 = GetSafeString(reader, "categorie2"),
                            Categorie3 = GetSafeString(reader, "categorie3"),
                            TempsTotal = GetSafeInt(reader, "tempsTotal"),
                            Titre = GetSafeString(reader, "titre"),
                            Description = GetSafeString(reader, "description"),
                            DateRappel = GetSafeDateTime(reader, "dateRappel"),
                            DateResolution = GetSafeDateTime(reader, "dateResolution"),
                            StatusTemps = GetSafeInt(reader, "statusTemps"),
                            Avertissement = GetSafeInt(reader, "avertissement"),
                            DernierCorrespondant = GetSafeString(reader, "dernierCorrespondant"),
                            DatePremiereReponse = GetSafeDateTime(reader, "datePremiereReponse"),
                            DateDerniereReponse = GetSafeDateTime(reader, "dateDerniereReponse"),
                            NotificationEnabled = GetSafeBool(reader, "notificationEnabled")
                        });
                    }
                }
            }
            CloseConnection();
            return tickets;
        }

        public override Tickets GetById(int id)
        {
            Tickets ticket = null;
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM Tickets WHERE idTickets = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        ticket = new Tickets
                        {
                            IdTickets = GetSafeInt(reader, "idTickets"),
                            CtNum = GetSafeString(reader, "ct_num"),
                            Client = GetSafeString(reader, "client"),
                            DateCreation = GetSafeDateTime(reader, "dateCreation"),
                            Demandeur = GetSafeString(reader, "demandeur"),
                            Assigne = GetSafeString(reader, "assigne"),
                            Pole = GetSafeString(reader, "pole"),
                            Type = GetSafeString(reader, "type"),
                            Status = GetSafeString(reader, "status"),
                            Priorite = GetSafeString(reader, "priorite"),
                            Niveau = GetSafeInt(reader, "niveau"),
                            Categorie = GetSafeString(reader, "categorie"),
                            Categorie2 = GetSafeString(reader, "categorie2"),
                            Categorie3 = GetSafeString(reader, "categorie3"),
                            TempsTotal = GetSafeInt(reader, "tempsTotal"),
                            Titre = GetSafeString(reader, "titre"),
                            Description = GetSafeString(reader, "description"),
                            DateRappel = GetSafeDateTime(reader, "dateRappel"),
                            DateResolution = GetSafeDateTime(reader, "dateResolution"),
                            StatusTemps = GetSafeInt(reader, "statusTemps"),
                            Avertissement = GetSafeInt(reader, "avertissement"),
                            DernierCorrespondant = GetSafeString(reader, "dernierCorrespondant"),
                            DatePremiereReponse = GetSafeDateTime(reader, "datePremiereReponse"),
                            DateDerniereReponse = GetSafeDateTime(reader, "dateDerniereReponse"),
                            NotificationEnabled = GetSafeBool(reader, "notificationEnabled")
                        };
                    }
                }
            }
            CloseConnection();
            return ticket;
        }

        public override void Add(Tickets entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("INSERT INTO Tickets (ct_num, client, dateCreation, demandeur, assigne, pole, type, status, priorite, niveau, categorie, categorie2, categorie3, tempsTotal, titre, description, dateRappel, dateResolution, statusTemps, avertissement, dernierCorrespondant, datePremiereReponse, dateDerniereReponse, notificationEnabled) VALUES (@ct_num, @client, @dateCreation, @demandeur, @assigne, @pole, @type, @status, @priorite, @niveau, @categorie, @categorie2, @categorie3, @tempsTotal, @titre, @description, @dateRappel, @dateResolution, @statusTemps, @avertissement, @dernierCorrespondant, @datePremiereReponse, @dateDerniereReponse, @notificationEnabled)", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@ct_num", entity.CtNum);
                command.Parameters.AddWithValue("@client", entity.Client);
                command.Parameters.AddWithValue("@dateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@demandeur", entity.Demandeur);
                command.Parameters.AddWithValue("@assigne", entity.Assigne);
                command.Parameters.AddWithValue("@pole", entity.Pole);
                command.Parameters.AddWithValue("@type", entity.Type);
                command.Parameters.AddWithValue("@status", entity.Status);
                command.Parameters.AddWithValue("@priorite", entity.Priorite);
                command.Parameters.AddWithValue("@niveau", entity.Niveau);
                command.Parameters.AddWithValue("@categorie", entity.Categorie);
                command.Parameters.AddWithValue("@categorie2", entity.Categorie2);
                command.Parameters.AddWithValue("@categorie3", entity.Categorie3);
                command.Parameters.AddWithValue("@tempsTotal", entity.TempsTotal);
                command.Parameters.AddWithValue("@titre", entity.Titre);
                command.Parameters.AddWithValue("@description", entity.Description);
                command.Parameters.AddWithValue("@dateRappel", entity.DateRappel);
                command.Parameters.AddWithValue("@dateResolution", entity.DateResolution);
                command.Parameters.AddWithValue("@statusTemps", entity.StatusTemps);
                command.Parameters.AddWithValue("@avertissement", entity.Avertissement);
                command.Parameters.AddWithValue("@dernierCorrespondant", entity.DernierCorrespondant);
                command.Parameters.AddWithValue("@datePremiereReponse", entity.DatePremiereReponse);
                command.Parameters.AddWithValue("@dateDerniereReponse", entity.DateDerniereReponse);
                command.Parameters.AddWithValue("@notificationEnabled", entity.NotificationEnabled);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Tickets entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("UPDATE Tickets SET ct_num = @ct_num, client = @client, dateCreation = @dateCreation, demandeur = @demandeur, assigne = @assigne, pole = @pole, type = @type, status = @status, priorite = @priorite, niveau = @niveau, categorie = @categorie, categorie2 = @categorie2, categorie3 = @categorie3, tempsTotal = @tempsTotal, titre = @titre, description = @description, dateRappel = @dateRappel, dateResolution = @dateResolution, statusTemps = @statusTemps, avertissement = @avertissement, dernierCorrespondant = @dernierCorrespondant, datePremiereReponse = @datePremiereReponse, dateDerniereReponse = @dateDerniereReponse, notificationEnabled = @notificationEnabled WHERE idTickets = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@ct_num", entity.CtNum);
                command.Parameters.AddWithValue("@client", entity.Client);
                command.Parameters.AddWithValue("@dateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@demandeur", entity.Demandeur);
                command.Parameters.AddWithValue("@assigne", entity.Assigne);
                command.Parameters.AddWithValue("@pole", entity.Pole);
                command.Parameters.AddWithValue("@type", entity.Type);
                command.Parameters.AddWithValue("@status", entity.Status);
                command.Parameters.AddWithValue("@priorite", entity.Priorite);
                command.Parameters.AddWithValue("@niveau", entity.Niveau);
                command.Parameters.AddWithValue("@categorie", entity.Categorie);
                command.Parameters.AddWithValue("@categorie2", entity.Categorie2);
                command.Parameters.AddWithValue("@categorie3", entity.Categorie3);
                command.Parameters.AddWithValue("@tempsTotal", entity.TempsTotal);
                command.Parameters.AddWithValue("@titre", entity.Titre);
                command.Parameters.AddWithValue("@description", entity.Description);
                command.Parameters.AddWithValue("@dateRappel", entity.DateRappel);
                command.Parameters.AddWithValue("@dateResolution", entity.DateResolution);
                command.Parameters.AddWithValue("@statusTemps", entity.StatusTemps);
                command.Parameters.AddWithValue("@avertissement", entity.Avertissement);
                command.Parameters.AddWithValue("@dernierCorrespondant", entity.DernierCorrespondant);
                command.Parameters.AddWithValue("@datePremiereReponse", entity.DatePremiereReponse);
                command.Parameters.AddWithValue("@dateDerniereReponse", entity.DateDerniereReponse);
                command.Parameters.AddWithValue("@notificationEnabled", entity.NotificationEnabled);
                command.Parameters.AddWithValue("@id", entity.IdTickets);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Tickets entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("DELETE FROM Tickets WHERE idTickets = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", entity.IdTickets);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}