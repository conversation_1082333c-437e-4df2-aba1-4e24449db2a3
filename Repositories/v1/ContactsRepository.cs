using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class ContactsRepository : Repository<Contacts>
    {
        public ContactsRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Contacts> GetAll()
        {
            var contacts = new List<Contacts>();
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM Contacts", (SqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        contacts.Add(new Contacts
                        {
                            Id = GetSafeInt(reader, "idContacts"),
                            Nom = GetSafeString(reader, "nom"),
                            Prenom = GetSafeString(reader, "prenom"),
                            Civilite = GetSafeString(reader, "civilite"),
                            Email = GetSafeString(reader, "email"),
                            Telephone = GetSafeString(reader, "telephone"),
                            Telephone2 = GetSafeString(reader, "telephone2"),
                            Telephone3 = GetSafeString(reader, "telephone3"),
                            Mobile = GetSafeString(reader, "mobile"),
                            Fonction = GetSafeString(reader, "fonction"),
                            CtNum = GetSafeString(reader, "ct_num"),
                            CtNo = GetSafeString(reader, "ct_no"),
                            DateCreation = GetSafeDateTime(reader, "dateCreation"),
                            DateModification = GetSafeDateTime(reader, "dateModification"),
                            Note = GetSafeString(reader, "note"),
                            Systematique = GetSafeBool(reader, "systematique"),
                            Facturation = GetSafeBool(reader, "facturation"),
                            Relance = GetSafeBool(reader, "relance")
                        });
                    }
                }
            }
            CloseConnection();
            return contacts;
        }

        public override Contacts GetById(int id)
        {
            Contacts contact = null;
            OpenConnection();
            using (var command = new SqlCommand("SELECT * FROM Contacts WHERE idContacts = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        contact = new Contacts
                        {
                            Id = GetSafeInt(reader, "idContacts"),
                            Nom = GetSafeString(reader, "nom"),
                            Prenom = GetSafeString(reader, "prenom"),
                            Civilite = GetSafeString(reader, "civilite"),
                            Email = GetSafeString(reader, "email"),
                            Telephone = GetSafeString(reader, "telephone"),
                            Telephone2 = GetSafeString(reader, "telephone2"),
                            Telephone3 = GetSafeString(reader, "telephone3"),
                            Mobile = GetSafeString(reader, "mobile"),
                            Fonction = GetSafeString(reader, "fonction"),
                            CtNum = GetSafeString(reader, "ct_num"),
                            CtNo = GetSafeString(reader, "ct_no"),
                            DateCreation = GetSafeDateTime(reader, "dateCreation"),
                            DateModification = GetSafeDateTime(reader, "dateModification"),
                            Note = GetSafeString(reader, "note"),
                            Systematique = GetSafeBool(reader, "systematique"),
                            Facturation = GetSafeBool(reader, "facturation"),
                            Relance = GetSafeBool(reader, "relance")
                        };
                    }
                }
            }
            CloseConnection();
            return contact;
        }

        public override void Add(Contacts entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("INSERT INTO Contacts (nom, prenom, civilite, email, telephone, telephone2, telephone3, mobile, fonction, ct_num, ct_no, dateCreation, dateModification, note, systematique, facturation, relance) VALUES (@nom, @prenom, @civilite, @email, @telephone, @telephone2, @telephone3, @mobile, @fonction, @ct_num, @ct_no, @dateCreation, @dateModification, @note, @systematique, @facturation, @relance)", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@nom", entity.Nom);
                command.Parameters.AddWithValue("@prenom", entity.Prenom);
                command.Parameters.AddWithValue("@civilite", entity.Civilite);
                command.Parameters.AddWithValue("@email", entity.Email);
                command.Parameters.AddWithValue("@telephone", entity.Telephone);
                command.Parameters.AddWithValue("@telephone2", entity.Telephone2);
                command.Parameters.AddWithValue("@telephone3", entity.Telephone3);
                command.Parameters.AddWithValue("@mobile", entity.Mobile);
                command.Parameters.AddWithValue("@fonction", entity.Fonction);
                command.Parameters.AddWithValue("@ct_num", entity.CtNum);
                command.Parameters.AddWithValue("@ct_no", entity.CtNo);
                command.Parameters.AddWithValue("@dateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@dateModification", entity.DateModification);
                command.Parameters.AddWithValue("@note", entity.Note);
                command.Parameters.AddWithValue("@systematique", entity.Systematique);
                command.Parameters.AddWithValue("@facturation", entity.Facturation);
                command.Parameters.AddWithValue("@relance", entity.Relance);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Contacts entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("UPDATE Contacts SET nom = @nom, prenom = @prenom, civilite = @civilite, email = @email, telephone = @telephone, telephone2 = @telephone2, telephone3 = @telephone3, mobile = @mobile, fonction = @fonction, ct_num = @ct_num, ct_no = @ct_no, dateCreation = @dateCreation, dateModification = @dateModification, note = @note, systematique = @systematique, facturation = @facturation, relance = @relance WHERE idContacts = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@nom", entity.Nom);
                command.Parameters.AddWithValue("@prenom", entity.Prenom);
                command.Parameters.AddWithValue("@civilite", entity.Civilite);
                command.Parameters.AddWithValue("@email", entity.Email);
                command.Parameters.AddWithValue("@telephone", entity.Telephone);
                command.Parameters.AddWithValue("@telephone2", entity.Telephone2);
                command.Parameters.AddWithValue("@telephone3", entity.Telephone3);
                command.Parameters.AddWithValue("@mobile", entity.Mobile);
                command.Parameters.AddWithValue("@fonction", entity.Fonction);
                command.Parameters.AddWithValue("@ct_num", entity.CtNum);
                command.Parameters.AddWithValue("@ct_no", entity.CtNo);
                command.Parameters.AddWithValue("@dateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@dateModification", entity.DateModification);
                command.Parameters.AddWithValue("@note", entity.Note);
                command.Parameters.AddWithValue("@systematique", entity.Systematique);
                command.Parameters.AddWithValue("@facturation", entity.Facturation);
                command.Parameters.AddWithValue("@relance", entity.Relance);
                command.Parameters.AddWithValue("@id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Contacts entity)
        {
            OpenConnection();
            using (var command = new SqlCommand("DELETE FROM Contacts WHERE idContacts = @id", (SqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", entity.Id);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}