using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models;
using Microsoft.Data.Sqlite;

namespace HeliosETL.Repositories
{
    public class ETLHistoryRepository : Repository<ETLHistory>
    {
        public ETLHistoryRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<ETLHistory> GetAll()
        {
            var etlHistories = new List<ETLHistory>();
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLHistory", (SqliteConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        etlHistories.Add(new ETLHistory
                        {
                            oldId = GetSafeLong(reader, "oldId"),
                            newId = GetSafeLong(reader, "newId"),
                            tableName = GetSafeString(reader, "tableName"),
                            action = GetSafeString(reader, "action"),
                            errorMessage = GetSafeString(reader, "errorMessage"),
                            state = GetSafeEnum<ETLState>(reader, "state", ETLState.Failed),
                            date = GetSafeDateTime(reader, "date")
                        });
                    }
                }
            }
            CloseConnection();
            return etlHistories;
        }

        public override ETLHistory GetById(int id)
        {
            ETLHistory etlHistory = null;
            OpenConnection();
            using (var command = new SqliteCommand("SELECT * FROM ETLHistory WHERE oldId = @id", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        etlHistory = new ETLHistory
                        {
                            oldId = GetSafeLong(reader, "oldId"),
                            newId = GetSafeLong(reader, "newId"),
                            tableName = GetSafeString(reader, "tableName"),
                            action = GetSafeString(reader, "action"),
                            errorMessage = GetSafeString(reader, "errorMessage"),
                            state = GetSafeEnum<ETLState>(reader, "state", ETLState.Failed),
                            date = GetSafeDateTime(reader, "date")
                        };
                    }
                }
            }
            CloseConnection();
            return etlHistory;
        }

        public override void Add(ETLHistory entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand("INSERT INTO ETLHistory (oldId, newId, tableName, action, errorMessage, state, date) VALUES (@oldId, @newId, @tableName, @action, @errorMessage, @state, @date)", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@oldId", entity.oldId);
                command.Parameters.AddWithValue("@newId", entity.newId);
                command.Parameters.AddWithValue("@tableName", entity.tableName);
                command.Parameters.AddWithValue("@action", entity.action);
                command.Parameters.AddWithValue("@errorMessage", entity.errorMessage);
                command.Parameters.AddWithValue("@state", entity.state.ToString());
                command.Parameters.AddWithValue("@date", entity.date);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(ETLHistory entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand("UPDATE ETLHistory SET newId = @newId, tableName = @tableName, action = @action, errorMessage = @errorMessage, state = @state, date = @date WHERE oldId = @oldId", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@newId", entity.newId);
                command.Parameters.AddWithValue("@tableName", entity.tableName);
                command.Parameters.AddWithValue("@action", entity.action);
                command.Parameters.AddWithValue("@errorMessage", entity.errorMessage);
                command.Parameters.AddWithValue("@state", entity.state.ToString());
                command.Parameters.AddWithValue("@date", entity.date);
                command.Parameters.AddWithValue("@oldId", entity.oldId);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(ETLHistory entity)
        {
            OpenConnection();
            using (var command = new SqliteCommand("DELETE FROM ETLHistory WHERE oldId = @oldId", (SqliteConnection)_connection))
            {
                command.Parameters.AddWithValue("@oldId", entity.oldId);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}