using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using HeliosETL.Services;
using Microsoft.Data.SqlClient;
using Microsoft.Data.Sqlite;
using MySql.Data.MySqlClient;
using Serilog;

namespace HeliosETL.Repositories
{
    public abstract class Repository<TEntity> : IRepository<TEntity> where TEntity : class
    {
        protected readonly DbConnection _connection;
        protected bool _shouldCloseConnection;
        private readonly ILogger _logger;

        protected Repository(DbConnection connection)
        {
            _connection = connection;
            _logger = LoggingService.Instance.GetLogger<Repository<TEntity>>();
        }

        public abstract IEnumerable<TEntity> GetAll();
        public abstract TEntity GetById(int id);
        public abstract void Add(TEntity entity);
        public abstract void Update(TEntity entity);
        public abstract void Delete(TEntity entity);

        protected void OpenConnection()
        {
            _shouldCloseConnection = _connection.State != ConnectionState.Open;
            if (_shouldCloseConnection)
            {
                _connection.Open();
            }
        }

        protected void CloseConnection()
        {
            if (_shouldCloseConnection && _connection.State == ConnectionState.Open)
            {
                _connection.Close();
            }
        }

        // Helper methods to safely handle DBNull values
        protected static string GetSafeString(IDataReader reader, string columnName)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? string.Empty : value.ToString() ?? string.Empty;
        }

        protected static int GetSafeInt(IDataReader reader, string columnName, int defaultValue = 0)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToInt32(value);
        }

        protected static long GetSafeLong(IDataReader reader, string columnName, long defaultValue = 0)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToInt64(value);
        }

        protected static DateTime GetSafeDateTime(IDataReader reader, string columnName, DateTime? defaultValue = null)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? (defaultValue ?? DateTime.MinValue) : Convert.ToDateTime(value);
        }

        protected static bool GetSafeBool(IDataReader reader, string columnName, bool defaultValue = false)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToBoolean(value);
        }

        protected static decimal GetSafeDecimal(IDataReader reader, string columnName, decimal defaultValue = 0)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToDecimal(value);
        }

        protected static double GetSafeDouble(IDataReader reader, string columnName, double defaultValue = 0)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToDouble(value);
        }

        protected static byte GetSafeByte(IDataReader reader, string columnName, byte defaultValue = 0)
        {
            var value = reader[columnName];
            return value == DBNull.Value ? defaultValue : Convert.ToByte(value);
        }

        protected static T GetSafeEnum<T>(IDataReader reader, string columnName, T defaultValue = default(T)) where T : struct, Enum
        {
            var value = reader[columnName];
            if (value == DBNull.Value) return defaultValue;

            var stringValue = value.ToString();
            return Enum.TryParse<T>(stringValue, out var result) ? result : defaultValue;
        }

        protected static T? GetSafeNullable<T>(IDataReader reader, string columnName) where T : struct
        {
            var value = reader[columnName];
            return value == DBNull.Value ? null : (T)Convert.ChangeType(value, typeof(T));
        }
        
        protected static Dictionary<string, string> GetSafeJSONAsDictionary(IDataReader reader, string columnName)
        {
            var result = new Dictionary<string, string>();
            var value = reader[columnName];
    
            if (value == DBNull.Value || value == null)
                return result;
    
            var jsonString = value.ToString();
            if (string.IsNullOrWhiteSpace(jsonString))
                return result;
    
            try
            {
                // Handle the case where the JSON might be just "{}" or empty object
                if (jsonString.Trim() == "{}")
                    return result;
            
                var jsonDocument = System.Text.Json.JsonDocument.Parse(jsonString);
        
                foreach (var property in jsonDocument.RootElement.EnumerateObject())
                {
                    // Convert all values to strings since the Data property is Dictionary<string, string>
                    var stringValue = property.Value.ValueKind switch
                    {
                        System.Text.Json.JsonValueKind.String => property.Value.GetString() ?? string.Empty,
                        System.Text.Json.JsonValueKind.Number => property.Value.ToString(),
                        System.Text.Json.JsonValueKind.True => "true",
                        System.Text.Json.JsonValueKind.False => "false",
                        System.Text.Json.JsonValueKind.Null => string.Empty,
                        _ => property.Value.ToString()
                    };
            
                    result[property.Name] = stringValue;
                }
            }
            catch (System.Text.Json.JsonException ex)
            {
                return new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                return new Dictionary<string, string>();
            }
    
            return result;
        }
        
        protected static Dictionary<string, string> GetSafeDataMap(IDataReader reader, string columnName, char pairSeparator = ';', char keyValueSeparator = '=')
        {
            var result = new Dictionary<string, string>();
            var value = reader[columnName];
    
            if (value == DBNull.Value || value == null)
                return result;
    
            var optionsString = value.ToString();
            if (string.IsNullOrWhiteSpace(optionsString))
                return result;
    
            try
            {
                var pairs = optionsString.Split(pairSeparator, StringSplitOptions.RemoveEmptyEntries);
        
                foreach (var pair in pairs)
                {
                    var keyValue = pair.Split(keyValueSeparator, 2, StringSplitOptions.None);
                    if (keyValue.Length == 2)
                    {
                        var key = keyValue[0].Trim();
                        var val = keyValue[1].Trim();
                
                        if (!string.IsNullOrEmpty(key))
                        {
                            result[key] = val;
                        }
                    }
                }
            }
            catch (Exception)
            {
                return new Dictionary<string, string>();
            }
    
            return result;
        }
    }
}