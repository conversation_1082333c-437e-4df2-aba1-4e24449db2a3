using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class CommanditaireRepository : Repository<Commanditaire>
    {
        public CommanditaireRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Commanditaire> GetAll()
        {
            var commanditaires = new List<Commanditaire>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_commanditaire", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        commanditaires.Add(new Commanditaire
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            Data = GetSafeJSONAsDictionary(reader, "Data")
                            
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var commanditaire in commanditaires)
            {
                commanditaire.Personnes = GetPersonnesByCommanditaire(commanditaire.Oid);
            }
            
            return commanditaires;
        }

        public HashSet<Personne> GetPersonnesByCommanditaire(long oidCommanditaire)
        {
            HashSet<Personne> personnes = new HashSet<Personne>();
            
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_personne 
                             WHERE Oid IN (
                             SELECT personne_oid FROM hls_commanditaire_has_personne WHERE commanditaire_oid = @oidCommanditaire
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidCommanditaire", oidCommanditaire);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        personnes.Add(new Personne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Prenom = GetSafeString(reader, "Prenom"),
                            Fonction = GetSafeString(reader, "Fonction"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            ExternalSource = new SourceOfData()
                            {
                                Oid = GetSafeLong(reader, "src_oid"),
                                Kind = GetSafeString(reader, "src_kind"),
                                Name = GetSafeString(reader, "src_name"),
                                DateUpdated = GetSafeDateTime(reader, "src_date")
                            }
                        });
                    }
                }
            }

            return personnes;
        }

        public override Commanditaire GetById(int id)
        {
            Commanditaire commanditaire = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_commanditaire WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        commanditaire = new Commanditaire
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                            Data = GetSafeJSONAsDictionary(reader, "Data"),
                            ExternalSource = new SourceOfData()
                            {
                                Oid = GetSafeLong(reader, "src_oid"),
                                Kind = GetSafeString(reader, "src_kind"),
                                Name = GetSafeString(reader, "src_name"),
                                DateUpdated = GetSafeDateTime(reader, "src_date")
                            }
                        };
                    }
                }
            }
            CloseConnection();
            
            if (commanditaire != null)
            {
                commanditaire.Personnes = GetPersonnesByCommanditaire(commanditaire.Oid);
            }
            
            return commanditaire;
        }

        public override void Add(Commanditaire entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_commanditaire (Oid, __version, Externe, Nom, Email, Telephone) VALUES (@Oid, @__version, @Externe, @Nom, @Email, @Telephone)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Externe", entity.Externe);
                command.Parameters.AddWithValue("@Nom", entity.Nom);
                command.Parameters.AddWithValue("@Email", entity.Email);
                command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Commanditaire entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_commanditaire SET __version = @__version, Externe = @Externe, Nom = @Nom, Email = @Email, Telephone = @Telephone WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Externe", entity.Externe);
                command.Parameters.AddWithValue("@Nom", entity.Nom);
                command.Parameters.AddWithValue("@Email", entity.Email);
                command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Commanditaire entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_commanditaire WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}