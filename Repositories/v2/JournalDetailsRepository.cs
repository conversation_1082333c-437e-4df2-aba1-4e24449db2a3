using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class JournalDetailsRepository : Repository<JournalDetails>
    {
        public JournalDetailsRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<JournalDetails> GetAll()
        {
            var journalDetails = new List<JournalDetails>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_journal_details", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        journalDetails.Add(new JournalDetails
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            ProprieteType = GetSafeString(reader, "ProprieteType"),
                            Propriete = GetSafeString(reader, "Propriete"),
                            AncienneValeur = GetSafeString(reader, "AncienneValeur"),
                            NouvelleValeur = GetSafeString(reader, "NouvelleValeur")
                        });
                    }
                }
            }
            CloseConnection();
            return journalDetails;
        }

        public override JournalDetails GetById(int id)
        {
            JournalDetails journalDetail = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_journal_details WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        journalDetail = new JournalDetails
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            ProprieteType = GetSafeString(reader, "ProprieteType"),
                            Propriete = GetSafeString(reader, "Propriete"),
                            AncienneValeur = GetSafeString(reader, "AncienneValeur"),
                            NouvelleValeur = GetSafeString(reader, "NouvelleValeur")
                        };
                    }
                }
            }
            CloseConnection();
            return journalDetail;
        }

        public override void Add(JournalDetails entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_journal_details (Oid, __version, ProprieteType, Propriete, AncienneValeur, NouvelleValeur) VALUES (@Oid, @__version, @ProprieteType, @Propriete, @AncienneValeur, @NouvelleValeur)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@ProprieteType", entity.ProprieteType);
                command.Parameters.AddWithValue("@Propriete", entity.Propriete);
                command.Parameters.AddWithValue("@AncienneValeur", entity.AncienneValeur);
                command.Parameters.AddWithValue("@NouvelleValeur", entity.NouvelleValeur);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(JournalDetails entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_journal_details SET __version = @__version, ProprieteType = @ProprieteType, Propriete = @Propriete, AncienneValeur = @AncienneValeur, NouvelleValeur = @NouvelleValeur WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@ProprieteType", entity.ProprieteType);
                command.Parameters.AddWithValue("@Propriete", entity.Propriete);
                command.Parameters.AddWithValue("@AncienneValeur", entity.AncienneValeur);
                command.Parameters.AddWithValue("@NouvelleValeur", entity.NouvelleValeur);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(JournalDetails entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_journal_details WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}