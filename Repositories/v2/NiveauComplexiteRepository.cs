using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class NiveauComplexiteRepository : Repository<NiveauComplexite>
    {
        public NiveauComplexiteRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<NiveauComplexite> GetAll()
        {
            var niveauComplexites = new List<NiveauComplexite>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_niveau_complexite", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        niveauComplexites.Add(new NiveauComplexite
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete")
                        });
                    }
                }
            }
            CloseConnection();
            return niveauComplexites;
        }

        public override NiveauComplexite GetById(int id)
        {
            NiveauComplexite niveauComplexite = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_niveau_complexite WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        niveauComplexite = new NiveauComplexite
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete")
                        };
                    }
                }
            }
            CloseConnection();
            return niveauComplexite;
        }

        public override void Add(NiveauComplexite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_niveau_complexite (Oid, __version, Code, Libelle, Description, Obsolete) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(NiveauComplexite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_niveau_complexite SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(NiveauComplexite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_niveau_complexite WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}