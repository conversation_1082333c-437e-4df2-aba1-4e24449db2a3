using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypeMissionRepository : Repository<TypeMission>
    {
        public TypeMissionRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<TypeMission> GetAll()
        {
            var typeMissions = new List<TypeMission>();
            OpenConnection();
            using (var command = new MySqlCommand(@"SELECT * FROM hls_mission_type", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        typeMissions.Add(new TypeMission
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var typeMission in typeMissions)
            {
                typeMission.DomainesMetier = GetDomainesMetierByTypeMission(typeMission.Oid);
            }
            
            return typeMissions;
        }

        public HashSet<DomaineMetier> GetDomainesMetierByTypeMission(long oidTypeMission)
        {
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_domaine_metier 
                             WHERE Oid IN (
                             SELECT domaine_metier_oid FROM hls_mission_type_has_domaine_metier WHERE mission_type_oid = @oidTypeMission
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidTypeMission", oidTypeMission);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        domainesMetier.Add(new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            CloseConnection();
            return domainesMetier;
        }

        public override TypeMission GetById(int id)
        {
            TypeMission typeMission = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_mission_type WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        typeMission = new TypeMission
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        };
                    }
                }
            }
            CloseConnection();
            
            if (typeMission != null)
            {
                typeMission.DomainesMetier = GetDomainesMetierByTypeMission(typeMission.Oid);
            }
            
            return typeMission;
        }

        public override void Add(TypeMission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_mission_type (Oid, __version, Code, Libelle, Description, Obsolete) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(TypeMission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_mission_type SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(TypeMission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_mission_type WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}