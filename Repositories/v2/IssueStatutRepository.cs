using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueStatutRepository : Repository<IssueStatut>
    {
        public IssueStatutRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<IssueStatut> GetAll()
        {
            var issueStatuts = new List<IssueStatut>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_statut", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issueStatuts.Add(new IssueStatut
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Nouveau = GetSafeBool(reader, "Nouveau"),
                            Ferme = GetSafeBool(reader, "Ferme")
                        });
                    }
                }
            }
            CloseConnection();
            return issueStatuts;
        }

        public override IssueStatut GetById(int id)
        {
            IssueStatut issueStatut = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_statut WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issueStatut = new IssueStatut
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Nouveau = GetSafeBool(reader, "Nouveau"),
                            Ferme = GetSafeBool(reader, "Ferme")
                        };
                    }
                }
            }
            CloseConnection();
            return issueStatut;
        }

        public override void Add(IssueStatut entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_issue_statut (Oid, __version, Code, Libelle, Description, Obsolete, Nouveau, Ferme) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete, @Nouveau, @Ferme)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Nouveau", entity.Nouveau);
                command.Parameters.AddWithValue("@Ferme", entity.Ferme);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(IssueStatut entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_issue_statut SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete, Nouveau = @Nouveau, Ferme = @Ferme WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Nouveau", entity.Nouveau);
                command.Parameters.AddWithValue("@Ferme", entity.Ferme);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(IssueStatut entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_issue_statut WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}