using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class JournalRepository : Repository<Journal>
    {
        public JournalRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Journal> GetAll()
        {
            var journals = new List<Journal>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_journal", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        journals.Add(new Journal
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Note = GetSafeString(reader, "Note"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            Prive = GetSafeBool(reader, "Prive"),
                            Interne = GetSafeBool(reader, "Interne")
                        });
                    }
                }
            }
            CloseConnection();
            return journals;
        }

        public override Journal GetById(int id)
        {
            Journal journal = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_journal WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        journal = new Journal
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Note = GetSafeString(reader, "Note"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            Prive = GetSafeBool(reader, "Prive"),
                            Interne = GetSafeBool(reader, "Interne")
                        };
                    }
                }
            }
            CloseConnection();
            return journal;
        }

        public override void Add(Journal entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_journal (Oid, __version, Note, DateCreation, Prive, Interne) VALUES (@Oid, @__version, @Note, @DateCreation, @Prive, @Interne)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Note", entity.Note);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@Prive", entity.Prive);
                command.Parameters.AddWithValue("@Interne", entity.Interne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Journal entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_journal SET __version = @__version, Note = @Note, DateCreation = @DateCreation, Prive = @Prive, Interne = @Interne WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Note", entity.Note);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@Prive", entity.Prive);
                command.Parameters.AddWithValue("@Interne", entity.Interne);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Journal entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_journal WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}