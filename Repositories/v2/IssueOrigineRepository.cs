using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueOrigineRepository : Repository<IssueOrigine>
    {
        public IssueOrigineRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<IssueOrigine> GetAll()
        {
            var issuesOrigine = new List<IssueOrigine>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_origine", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issuesOrigine.Add(new IssueOrigine
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var issueOrigine in issuesOrigine)
            {
                issueOrigine.DomainesMetier = GetDomainesMetierByIssueOrigine(issueOrigine.Oid);
            }
            
            return issuesOrigine;
        }

        public HashSet<DomaineMetier> GetDomainesMetierByIssueOrigine(long oidIssueOrigine)
        {
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_domaine_metier 
                             WHERE Oid IN (
                             SELECT domaine_metier_oid FROM hls_issue_origine_type_has_domaine_metier WHERE issue_origine_oid = @oidIssueOrigine
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidIssueOrigine", oidIssueOrigine);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        domainesMetier.Add(new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }

            return domainesMetier;
        }

        public override IssueOrigine GetById(int id)
        {
            IssueOrigine issueOrigine = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_origine WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issueOrigine = new IssueOrigine
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        };
                    }
                }
            }
            CloseConnection();
            
            if (issueOrigine != null)
            {
                issueOrigine.DomainesMetier = GetDomainesMetierByIssueOrigine(issueOrigine.Oid);
            }
            
            return issueOrigine;
        }

        public override void Add(IssueOrigine entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_issue_origine (Oid, __version, Code, Libelle, Description, Obsolete) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(IssueOrigine entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_issue_origine SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(IssueOrigine entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_issue_origine WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}