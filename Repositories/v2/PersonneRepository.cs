using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class PersonneRepository : Repository<Personne>
    {
        public PersonneRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Personne> GetAll()
        {
            var personnes = new List<Personne>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_personne", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        personnes.Add(new Personne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Prenom = GetSafeString(reader, "Prenom"),
                            Fonction = GetSafeString(reader, "Fonction"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                        });
                    }
                }
            }
            CloseConnection();
            return personnes;
        }

        public override Personne GetById(int id)
        {
            Personne personne = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_personne WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        personne = new Personne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Externe = GetSafeBool(reader, "Externe"),
                            Nom = GetSafeString(reader, "Nom"),
                            Prenom = GetSafeString(reader, "Prenom"),
                            Fonction = GetSafeString(reader, "Fonction"),
                            Email = GetSafeString(reader, "Email"),
                            Telephone = GetSafeString(reader, "Telephone"),
                        };
                    }
                }
            }
            CloseConnection();
            return personne;
        }

        public override void Add(Personne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_personne (Oid, __version, Externe, Nom, Prenom, Fonction, Email, Telephone) VALUES (@Oid, @__version, @Externe, @Nom, @Prenom, @Fonction, @Email, @Telephone)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Externe", entity.Externe);
                command.Parameters.AddWithValue("@Nom", entity.Nom);
                command.Parameters.AddWithValue("@Prenom", entity.Prenom);
                command.Parameters.AddWithValue("@Fonction", entity.Fonction);
                command.Parameters.AddWithValue("@Email", entity.Email);
                command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Personne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_personne SET __version = @__version, Externe = @Externe, Nom = @Nom, Prenom = @Prenom, Fonction = @Fonction, Email = @Email, Telephone = @Telephone, Mobile = @Mobile WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Externe", entity.Externe);
                command.Parameters.AddWithValue("@Nom", entity.Nom);
                command.Parameters.AddWithValue("@Prenom", entity.Prenom);
                command.Parameters.AddWithValue("@Fonction", entity.Fonction);
                command.Parameters.AddWithValue("@Email", entity.Email);
                command.Parameters.AddWithValue("@Telephone", entity.Telephone);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Personne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_personne WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}