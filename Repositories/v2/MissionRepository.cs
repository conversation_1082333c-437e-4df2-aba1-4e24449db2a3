using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class MissionRepository : Repository<Mission>
    {
        public MissionRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<Mission> GetAll()
        {
            var missions = new List<Mission>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_mission", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        missions.Add(new Mission
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin")
                        });
                    }
                }
            }
            CloseConnection();
            return missions;
        }

        public override Mission GetById(int id)
        {
            Mission mission = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_mission WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        mission = new Mission
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin")
                        };
                    }
                }
            }
            CloseConnection();
            return mission;
        }

        public override void Add(Mission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_mission (Oid, __version, Code, Libelle, Description, DateCreation, DateModification, DatePrevisionnelleDebut, DatePrevisionnelleFin, DateEffectiveDebut, DateEffectiveFin) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @DateCreation, @DateModification, @DatePrevisionnelleDebut, @DatePrevisionnelleFin, @DateEffectiveDebut, @DateEffectiveFin)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(Mission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_mission SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, DateCreation = @DateCreation, DateModification = @DateModification, DatePrevisionnelleDebut = @DatePrevisionnelleDebut, DatePrevisionnelleFin = @DatePrevisionnelleFin, DateEffectiveDebut = @DateEffectiveDebut, DateEffectiveFin = @DateEffectiveFin WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(Mission entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_mission WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}