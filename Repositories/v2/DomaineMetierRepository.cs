using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class DomaineMetierRepository : Repository<DomaineMetier>
    {
        public DomaineMetierRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<DomaineMetier> GetAll()
        {
            var domainesMetier = new List<DomaineMetier>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_domaine_metier", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        domainesMetier.Add(new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            CloseConnection();
            return domainesMetier;
        }

        public override DomaineMetier GetById(int id)
        {
            DomaineMetier domaineMetier = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_domaine_metier WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        domaineMetier = new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        };
                    }
                }
            }
            CloseConnection();
            return domaineMetier;
        }

        public override void Add(DomaineMetier entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_domaine_metier (Oid, __version, Code, Libelle, Description, Obsolete, DateCreation) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete, @DateCreation)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(DomaineMetier entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_domaine_metier SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete, DateCreation = @DateCreation WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(DomaineMetier entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_domaine_metier WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}