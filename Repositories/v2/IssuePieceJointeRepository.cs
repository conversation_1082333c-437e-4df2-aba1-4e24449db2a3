using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssuePieceJointeRepository : Repository<IssuePieceJointe>
    {
        public IssuePieceJointeRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<IssuePieceJointe> GetAll()
        {
            var issuePieceJointes = new List<IssuePieceJointe>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_piece_jointe", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issuePieceJointes.Add(new IssuePieceJointe
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Fichier = GetSafeString(reader, "Fichier"),
                            TypeMime = GetSafeString(reader, "TypeMime"),
                            Taille = GetSafeLong(reader, "Taille"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            CloseConnection();
            return issuePieceJointes;
        }

        public override IssuePieceJointe GetById(int id)
        {
            IssuePieceJointe issuePieceJointe = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_piece_jointe WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issuePieceJointe = new IssuePieceJointe
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Fichier = GetSafeString(reader, "Fichier"),
                            TypeMime = GetSafeString(reader, "TypeMime"),
                            Taille = GetSafeLong(reader, "Taille"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        };
                    }
                }
            }
            CloseConnection();
            return issuePieceJointe;
        }

        public override void Add(IssuePieceJointe entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_issue_piece_jointe (Oid, __version, Libelle, Description, Fichier, TypeMime, Taille, DateCreation) VALUES (@Oid, @__version, @Libelle, @Description, @Fichier, @TypeMime, @Taille, @DateCreation)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Fichier", entity.Fichier);
                command.Parameters.AddWithValue("@TypeMime", entity.TypeMime);
                command.Parameters.AddWithValue("@Taille", entity.Taille);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(IssuePieceJointe entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_issue_piece_jointe SET __version = @__version, Libelle = @Libelle, Description = @Description, Fichier = @Fichier, TypeMime = @TypeMime, Taille = @Taille, DateCreation = @DateCreation WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Fichier", entity.Fichier);
                command.Parameters.AddWithValue("@TypeMime", entity.TypeMime);
                command.Parameters.AddWithValue("@Taille", entity.Taille);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(IssuePieceJointe entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_issue_piece_jointe WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}