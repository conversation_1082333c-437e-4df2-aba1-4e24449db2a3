using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypePersonneRepository : Repository<TypePersonne>
    {
        public TypePersonneRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<TypePersonne> GetAll()
        {
            var typePersonnes = new List<TypePersonne>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_type_personne", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        typePersonnes.Add(new TypePersonne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Interne = GetSafeBool(reader, "Interne")
                        });
                    }
                }
            }
            CloseConnection();
            return typePersonnes;
        }

        public override TypePersonne GetById(int id)
        {
            TypePersonne typePersonne = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_type_personne WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        typePersonne = new TypePersonne
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Interne = GetSafeBool(reader, "Interne")
                        };
                    }
                }
            }
            CloseConnection();
            return typePersonne;
        }

        public override void Add(TypePersonne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_type_personne (Oid, __version, Code, Libelle, Description, Obsolete, Interne) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete, @Interne)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Interne", entity.Interne);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(TypePersonne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_type_personne SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete, Interne = @Interne WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Interne", entity.Interne);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(TypePersonne entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_type_personne WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}