using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueRepository : Repository<AbstractIssue>
    {
        public IssueRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<AbstractIssue> GetAll()
        {
            var issues = new List<AbstractIssue>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issues.Add(new AbstractIssue
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes")
                        });
                    }
                }
            }
            CloseConnection();
            return issues;
        }

        public override AbstractIssue GetById(int id)
        {
            AbstractIssue issue = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issue = new AbstractIssue
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Sujet = GetSafeString(reader, "Sujet"),
                            Description = GetSafeString(reader, "Description"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation"),
                            DateModification = GetSafeDateTime(reader, "DateModification"),
                            DatePrevisionnelleDebut = GetSafeDateTime(reader, "DatePrevisionnelleDebut"),
                            DatePrevisionnelleFin = GetSafeDateTime(reader, "DatePrevisionnelleFin"),
                            DateEffectiveDebut = GetSafeDateTime(reader, "DateEffectiveDebut"),
                            DateEffectiveFin = GetSafeDateTime(reader, "DateEffectiveFin"),
                            KindOfActivite = GetSafeString(reader, "KindOfActivite"),
                            KindOfIssueParente = GetSafeString(reader, "KindOfIssueParente"),
                            Avancement = GetSafeByte(reader, "Avancement"),
                            TempsEstimeMinutes = GetSafeInt(reader, "TempsEstimeMinutes"),
                            TempsEffectifMinutes = GetSafeInt(reader, "TempsEffectifMinutes")
                        };
                    }
                }
            }
            CloseConnection();
            return issue;
        }

        public override void Add(AbstractIssue entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_issue (Oid, __version, Code, Sujet, Description, DateCreation, DateModification, DatePrevisionnelleDebut, DatePrevisionnelleFin, DateEffectiveDebut, DateEffectiveFin, KindOfActivite, KindOfIssueParente, Avancement, TempsEstimeMinutes, TempsEffectifMinutes) VALUES (@Oid, @__version, @Code, @Sujet, @Description, @DateCreation, @DateModification, @DatePrevisionnelleDebut, @DatePrevisionnelleFin, @DateEffectiveDebut, @DateEffectiveFin, @KindOfActivite, @KindOfIssueParente, @Avancement, @TempsEstimeMinutes, @TempsEffectifMinutes)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Sujet", entity.Sujet);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                command.Parameters.AddWithValue("@KindOfActivite", entity.KindOfActivite);
                command.Parameters.AddWithValue("@KindOfIssueParente", entity.KindOfIssueParente);
                command.Parameters.AddWithValue("@Avancement", entity.Avancement);
                command.Parameters.AddWithValue("@TempsEstimeMinutes", entity.TempsEstimeMinutes);
                command.Parameters.AddWithValue("@TempsEffectifMinutes", entity.TempsEffectifMinutes);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(AbstractIssue entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_issue SET __version = @__version, Code = @Code, Sujet = @Sujet, Description = @Description, DateCreation = @DateCreation, DateModification = @DateModification, DatePrevisionnelleDebut = @DatePrevisionnelleDebut, DatePrevisionnelleFin = @DatePrevisionnelleFin, DateEffectiveDebut = @DateEffectiveDebut, DateEffectiveFin = @DateEffectiveFin, KindOfActivite = @KindOfActivite, KindOfIssueParente = @KindOfIssueParente, Avancement = @Avancement, TempsEstimeMinutes = @TempsEstimeMinutes, TempsEffectifMinutes = @TempsEffectifMinutes WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Sujet", entity.Sujet);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@DateCreation", entity.DateCreation);
                command.Parameters.AddWithValue("@DateModification", entity.DateModification);
                command.Parameters.AddWithValue("@DatePrevisionnelleDebut", entity.DatePrevisionnelleDebut);
                command.Parameters.AddWithValue("@DatePrevisionnelleFin", entity.DatePrevisionnelleFin);
                command.Parameters.AddWithValue("@DateEffectiveDebut", entity.DateEffectiveDebut);
                command.Parameters.AddWithValue("@DateEffectiveFin", entity.DateEffectiveFin);
                command.Parameters.AddWithValue("@KindOfActivite", entity.KindOfActivite);
                command.Parameters.AddWithValue("@KindOfIssueParente", entity.KindOfIssueParente);
                command.Parameters.AddWithValue("@Avancement", entity.Avancement);
                command.Parameters.AddWithValue("@TempsEstimeMinutes", entity.TempsEstimeMinutes);
                command.Parameters.AddWithValue("@TempsEffectifMinutes", entity.TempsEffectifMinutes);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(AbstractIssue entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_issue WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}