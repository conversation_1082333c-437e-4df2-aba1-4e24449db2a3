using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssuePrioriteRepository : Repository<IssuePriorite>
    {
        public IssuePrioriteRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<IssuePriorite> GetAll()
        {
            var issuePriorites = new List<IssuePriorite>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_priorite", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        issuePriorites.Add(new IssuePriorite
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Grade = GetSafeByte(reader, "Grade"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        });
                    }
                }
            }
            CloseConnection();
            return issuePriorites;
        }

        public override IssuePriorite GetById(int id)
        {
            IssuePriorite issuePriorite = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_issue_priorite WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        issuePriorite = new IssuePriorite
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Grade = GetSafeByte(reader, "Grade"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        };
                    }
                }
            }
            CloseConnection();
            return issuePriorite;
        }

        public override void Add(IssuePriorite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_issue_priorite (Oid, __version, Code, Libelle, Description, Obsolete, Grade, Options) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete, @Grade, @Options)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Grade", entity.Grade);
                command.Parameters.AddWithValue("@Options", entity.OptionsString);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(IssuePriorite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_issue_priorite SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete, Grade = @Grade WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Grade", entity.Grade);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(IssuePriorite entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_issue_priorite WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}