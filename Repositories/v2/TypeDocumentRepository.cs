using System.Collections.Generic;
using System.Data.Common;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypeDocumentRepository : Repository<TypeDocument>
    {
        public TypeDocumentRepository(DbConnection connection) : base(connection)
        {
        }

        public override IEnumerable<TypeDocument> GetAll()
        {
            var typeDocuments = new List<TypeDocument>();
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_document_type", (MySqlConnection)_connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        typeDocuments.Add(new TypeDocument
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        });
                    }
                }
            }
            CloseConnection();
            
            foreach (var typeDocument in typeDocuments)
            {
                typeDocument.DomainesMetier = GetDomainesMetierByTypeDocument(typeDocument.Oid);
            }
            
            return typeDocuments;
        }

        public override TypeDocument GetById(int id)
        {
            TypeDocument typeDocument = null;
            OpenConnection();
            using (var command = new MySqlCommand("SELECT * FROM hls_document_type WHERE Oid = @id", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@id", id);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        typeDocument = new TypeDocument
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            Options = GetSafeJSONAsDictionary(reader, "Options")
                        };
                    }
                }
            }
            CloseConnection();
            
            if (typeDocument != null)
            {
                typeDocument.DomainesMetier = GetDomainesMetierByTypeDocument(typeDocument.Oid);
            }
            
            return typeDocument;
        }

        public HashSet<DomaineMetier> GetDomainesMetierByTypeDocument(long oidTypeDocument)
        {
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            
            OpenConnection();
            using (var command = new MySqlCommand(@"
                    SELECT * FROM hls_domaine_metier 
                             WHERE Oid IN (
                             SELECT domaine_metier_oid FROM hls_document_type_has_domaine_metier WHERE document_type_oid = @oidTypeDocument
                   )", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@oidTypeDocument", oidTypeDocument);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        domainesMetier.Add(new DomaineMetier
                        {
                            Oid = GetSafeLong(reader, "Oid"),
                            __version = GetSafeInt(reader, "__version"),
                            Code = GetSafeString(reader, "Code"),
                            Libelle = GetSafeString(reader, "Libelle"),
                            Description = GetSafeString(reader, "Description"),
                            Obsolete = GetSafeBool(reader, "Obsolete"),
                            DateCreation = GetSafeDateTime(reader, "DateCreation")
                        });
                    }
                }
            }
            
            return domainesMetier;
        }

        public override void Add(TypeDocument entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("INSERT INTO hls_document_type (Oid, __version, Code, Libelle, Description, Obsolete) VALUES (@Oid, @__version, @Code, @Libelle, @Description, @Obsolete)", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Update(TypeDocument entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("UPDATE hls_document_type SET __version = @__version, Code = @Code, Libelle = @Libelle, Description = @Description, Obsolete = @Obsolete WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@__version", entity.__version);
                command.Parameters.AddWithValue("@Code", entity.Code);
                command.Parameters.AddWithValue("@Libelle", entity.Libelle);
                command.Parameters.AddWithValue("@Description", entity.Description);
                command.Parameters.AddWithValue("@Obsolete", entity.Obsolete);
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }

        public override void Delete(TypeDocument entity)
        {
            OpenConnection();
            using (var command = new MySqlCommand("DELETE FROM hls_document_type WHERE Oid = @Oid", (MySqlConnection)_connection))
            {
                command.Parameters.AddWithValue("@Oid", entity.Oid);
                command.ExecuteNonQuery();
            }
            CloseConnection();
        }
    }
}